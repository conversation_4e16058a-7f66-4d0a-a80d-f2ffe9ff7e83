
import numpy as np
import pandas as pd
import yfinance as yf
import os
import sys
import base64
import logging
import threading
import queue
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
from datetime import datetime, timedelta
import traceback
from dataclasses import dataclass
from typing import Optional, Dict, List, Any, Tuple

from parameter_registry import ParameterRegistry, default_registry

# Bollinger width function from stdv.py
def bbw_from_close_only(df, length=20):
    """Calculate Bollinger Band Width from close prices only"""
    close = df['Close'] if 'Close' in df.columns else df['close']
    rolling_max = close.rolling(window=length).max()
    rolling_min = close.rolling(window=length).min()
    rolling_mean = close.rolling(window=length).mean()

    bbw = ((rolling_max - rolling_min) / rolling_mean) * 100
    return bbw

# Simple cache replacement
class SimpleCache:
    def __init__(self, capacity=10):
        self.capacity = capacity
        self.cache = {}

    def get(self, key):
        return self.cache.get(key)

    def put(self, key, value):
        if len(self.cache) >= self.capacity:
            # Remove oldest item
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        self.cache[key] = value

    def clear(self):
        self.cache.clear()

    def adjust_capacity(self, new_capacity):
        self.capacity = new_capacity
        # Remove excess items if needed
        while len(self.cache) > self.capacity:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
from signal_processor import SignalProcessor
from loading_screen import LoadingScreen
from enhanced_signal_markers import EnhancedScatterPlotItem
from signal_display import EnhancedSignalDisplay
from candlestick_chart import CandlestickItem, VectorItem

# Configure logging
logger = logging.getLogger(__name__)


class PriceBasedVectorItem(pg.GraphicsObject):
    """Custom VectorItem that colors background based on price position relative to vector"""

    def __init__(self, data, vector_values, wave_length=10,
                 line_color='purple', line_width=2,
                 bg_up_color=(0, 100, 0, 15), bg_down_color=(139, 0, 0, 15),
                 skip_candles=None, data_already_processed=False,
                 show_vector=True, show_candles=True):
        pg.GraphicsObject.__init__(self)
        # Store the data and The Line values
        self.data = data  # List of (t, open, high, low, close)
        self.vector_values = vector_values  # List of The Line values
        self.wave_length = wave_length

        # Style settings
        self.line_color = line_color
        self.line_width = line_width
        self.bg_up_color = bg_up_color  # Green when price above vector
        self.bg_down_color = bg_down_color  # Red when price below vector

        # Flag to indicate if data is already processed (from market_odds.py)
        self.data_already_processed = data_already_processed

        # Store the skip_candles value
        self.skip_candles = skip_candles

        # Store visibility flags
        self.show_vector = show_vector
        self.show_candles = show_candles

        # Generate the picture
        self.generatePicture()

    def generatePicture(self):
        """Generate The Line and background coloring based on price position relative to vector."""
        self.picture = QtGui.QPicture()
        p = QtGui.QPainter(self.picture)

        # Skip if no data
        if not self.data or not self.vector_values or len(self.data) != len(self.vector_values):
            p.end()
            return

        # Create arrays for x and y coordinates
        x_values = [item[0] for item in self.data]

        # Calculate chart boundaries once (for efficiency)
        chart_high = max([item[2] for item in self.data])
        chart_low = min([item[3] for item in self.data])
        price_range = chart_high - chart_low

        # Add margins to chart boundaries
        y_min = chart_low - price_range * 0.05  # Lowest price with margin
        y_max = chart_high + price_range * 0.05  # Highest price with margin

        # Determine background color for each bar based on price position relative to vector
        bar_colors = [None] * len(self.vector_values)

        # Use the explicitly passed skip_candles value if provided
        if self.skip_candles is not None:
            skip_candles = self.skip_candles
        elif not self.data_already_processed:
            # Get dynamic skip value from parameter registry only if data is not already processed
            try:
                from parameter_registry import default_registry
                vector_length = default_registry.get_value('vector_length')
                skip_candles = vector_length + 1
            except:
                skip_candles = 2  # Fallback to static 2 if parameter registry is not available
        else:
            skip_candles = 0  # No skipping needed for already processed data

        for i in range(len(self.vector_values)):
            # Skip the first few candles if needed
            if i < skip_candles:
                continue

            # Get the close price for this bar
            close_price = self.data[i][4]  # Close price is at index 4
            vector_price = self.vector_values[i]

            # Color based on price position relative to vector
            if close_price > vector_price:
                # Price above vector - green background
                bar_colors[i] = self.bg_up_color
            else:
                # Price below vector - red background
                bar_colors[i] = self.bg_down_color

        # Draw background for each bar
        candle_width = 1.0

        for i in range(len(self.vector_values)):
            # Skip if we don't have a background color for this bar
            if bar_colors[i] is None:
                continue

            # Calculate bar coordinates aligned with candle borders
            x1 = x_values[i] - candle_width/2  # Left edge of current candle
            x2 = x_values[i] + candle_width/2  # Right edge of current candle

            # Draw the background for this specific bar
            p.setPen(pg.mkPen(None))  # No border
            p.setBrush(pg.mkBrush(bar_colors[i]))

            # Draw a rectangle for this bar that extends from top to bottom of the chart
            p.drawRect(QtCore.QRectF(x1, y_min, x2-x1, y_max-y_min))

        # Draw The Line as a step line (only if show_vector is True)
        if self.show_vector:
            p.setPen(pg.mkPen(self.line_color, width=self.line_width))

            # Create a path for the step line
            path = QtGui.QPainterPath()

            # Calculate candle width (assuming uniform spacing)
            candle_width = 1.0

            # Start the path at index skip_candles
            if len(self.vector_values) > skip_candles:
                # Adjust x position to align with left edge of the candle at index skip_candles
                start_x = x_values[skip_candles] - candle_width/2
                path.moveTo(QtCore.QPointF(start_x, self.vector_values[skip_candles]))

                # Add step segments to the path starting from index skip_candles + 1
                for i in range(skip_candles + 1, len(self.vector_values)):
                    # Calculate x position aligned with candle border
                    curr_x = x_values[i] - candle_width/2    # Left edge of current candle

                    # Add horizontal segment to right edge of previous candle
                    path.lineTo(QtCore.QPointF(curr_x, self.vector_values[i-1]))

                    # Add vertical segment at the current candle
                    path.lineTo(QtCore.QPointF(curr_x, self.vector_values[i]))

            # Draw the path
            p.drawPath(path)

        p.end()

    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        return QtCore.QRectF(self.picture.boundingRect())

@dataclass
class PriceLevel:
    price: float
    color: str
    label: str
    line: Optional[pg.InfiniteLine] = None
    text: Optional[pg.TextItem] = None

class ImprinterWorker(QtCore.QObject):
    """Worker thread for rendering imprints in the background"""

    # Define signals
    finished = QtCore.pyqtSignal()
    progress = QtCore.pyqtSignal(int)
    result = QtCore.pyqtSignal(object)
    error = QtCore.pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.running = False
        self.task_queue = queue.Queue()
        self.worker_thread = None

    def start(self):
        """Start the worker thread"""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.running = True
            self.worker_thread = threading.Thread(target=self._process_queue)
            self.worker_thread.daemon = True
            self.worker_thread.start()

    def stop(self):
        """Stop the worker thread"""
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.task_queue.put(None)  # Signal to exit
            self.worker_thread.join(timeout=1.0)

    def add_task(self, task_type, **kwargs):
        """Add a task to the queue"""
        self.task_queue.put((task_type, kwargs))
        self.start()  # Ensure the worker is running

    def _process_queue(self):
        """Process tasks from the queue"""
        while self.running:
            try:
                task = self.task_queue.get(timeout=0.5)
                if task is None:  # Exit signal
                    break

                task_type, kwargs = task
                if task_type == 'render_imprints':
                    self._render_imprints(**kwargs)

                self.task_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                self.error.emit(str(e))

    def _render_imprints(self, pivot_transitions, display_options, batch_size=20, **kwargs):
        """Render imprints in batches"""
        try:
            if not pivot_transitions:
                self.result.emit(None)
                return

            # Sort transitions
            sorted_transitions = sorted(pivot_transitions, key=lambda x: x[0])

            # Process in batches
            total_batches = (len(sorted_transitions) + batch_size - 1) // batch_size
            results = []

            for batch_idx in range(total_batches):
                if not self.running:
                    break

                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(sorted_transitions))
                batch = sorted_transitions[start_idx:end_idx]

                # Process this batch
                batch_results = self._process_batch(batch, **kwargs)
                results.extend(batch_results)

                # Report progress
                progress_pct = int((batch_idx + 1) / total_batches * 100)
                self.progress.emit(progress_pct)

            # Return the complete results
            self.result.emit(results)
            self.finished.emit()

        except Exception as e:
            self.error.emit(f"Error rendering imprints: {str(e)}")

    def _process_batch(self, batch, **kwargs):
        """Process a batch of imprints"""
        # This is a placeholder - the actual implementation would create
        # the visual elements for each imprint in the batch
        return batch


class PeakTroughRays:
    def __init__(self, plot_widget):
        self.plot_widget = plot_widget
        self.rays = []
        self.peaks = []
        self.troughs = []
        self.open_states = {}
        self.current_trend = None
        self.highest_high = None
        self.lowest_low = None
        self.last_pivot_cross = None
        self.current_price = None
        self.current_extrema = None
        self.current_vector_price = None
    def find_peaks_and_troughs(self, data):
        self.peaks = []
        self.troughs = []
        self.open_states = {}
        if len(data) < 2:
            return
        pivot = data['Close'].iloc[0]
        was_above_pivot = data['Close'].iloc[0] > pivot
        current_high = None
        current_low = None

        # Track the current extrema (highest high and lowest low)
        current_extrema_high = data['High'].iloc[0]
        current_extrema_low = data['Low'].iloc[0]

        for idx, row in data.iterrows():
            # Update current extrema
            if row['High'] > current_extrema_high:
                current_extrema_high = row['High']
            if row['Low'] < current_extrema_low:
                current_extrema_low = row['Low']

            # Calculate percentage from current extrema
            price_pct = ((row['Close'] / pivot) - 1) * 100  # Keep this for pivot crossing detection

            # Use current extrema for high/low percentages
            high_pct = ((row['High'] / current_extrema_high) - 1) * 100
            low_pct = ((row['Low'] / current_extrema_low) - 1) * 100

            is_above_pivot = price_pct > 0
            if was_above_pivot != is_above_pivot:
                if was_above_pivot:
                    if current_high is not None:
                        self.peaks.append(current_high)
                        self.open_states[current_high] = True
                    current_high = None
                    current_low = low_pct
                else:
                    if current_low is not None:
                        self.troughs.append(current_low)
                        self.open_states[current_low] = True
                    current_low = None
                    current_high = high_pct
                was_above_pivot = is_above_pivot
            else:
                if is_above_pivot:
                    if current_high is None or high_pct > current_high:
                        current_high = high_pct
                else:
                    if current_low is None or low_pct < current_low:
                        current_low = low_pct
    def draw_rays(self, data, end_idx):
        self.clear_rays()
        self.find_peaks_and_troughs(data)

    def clear_rays(self):
        for ray in self.rays:
            self.plot_widget.removeItem(ray)
        self.rays.clear()





class DummyClassifier:
    """Dummy classifier to replace ML functionality."""
    def __init__(self):
        self.is_trained = False

    def train(self, *args, **kwargs):
        return False

    def predict(self, *args, **kwargs):
        return None
















class DrawingTools:
    def __init__(self, plot_widget):
        self.plot_widget = plot_widget
        self.current_tool = None
        self.drawing_items = []
        self.current_item = None
        self.start_pos = None
        self.drawing = False
        self.selected_item = None
        self.moving_item = False
        self.item_labels = {}
        self.deleted_items = []  # Stack for undo functionality
        self.redone_items = []   # Stack for redo functionality
        self.anchored_texts = [] # List to track anchored text items
        self.colors = {
            'White': '#FFFFFF',
            'Red': '#FF0000',
            'Green': '#00FF00',
            'Blue': '#0000FF',
            'Yellow': '#FFFF00',
            'Magenta': '#FF00FF',
            'Cyan': '#00FFFF',
            'Orange': '#FFA500'
        }
    def start_drawing(self, pos, tool_type):
        self.current_tool = tool_type
        self.start_pos = pos
        self.drawing = True
        if tool_type == 'line':
            self.current_item = pg.PlotDataItem(pen=pg.mkPen('w', width=2))
        elif tool_type == 'rectangle':
            self.current_item = pg.PlotDataItem(pen=pg.mkPen('w', width=2))
        elif tool_type == 'horizontal':
            self.current_item = pg.InfiniteLine(angle=0, movable=True, pen=pg.mkPen('w', width=2))
        elif tool_type == 'vertical':
            self.current_item = pg.InfiniteLine(angle=90, movable=True, pen=pg.mkPen('w', width=2))
        elif tool_type == 'text':
            self.current_item = pg.TextItem(text="", color='w')
        if self.current_item:
            self.plot_widget.addItem(self.current_item)
    def change_item_color(self, item, color):
        if isinstance(item, pg.PlotDataItem):
            item.setPen(pg.mkPen(color, width=2))
        elif isinstance(item, pg.InfiniteLine):
            item.setPen(pg.mkPen(color, width=2))
        elif isinstance(item, pg.TextItem):
            item.setColor(color)
        if item in self.item_labels:
            self.item_labels[item].setColor(color)
    def add_label_to_item(self, item, text):
        if item in self.item_labels:
            self.plot_widget.removeItem(self.item_labels[item])
        if isinstance(item, pg.PlotDataItem):
            data = item.getData()
            if data[0] is not None:
                x, y = data
                mid_x = (min(x) + max(x)) / 2
                mid_y = (min(y) + max(y)) / 2
                color = item.opts['pen'].color().name()
        elif isinstance(item, pg.InfiniteLine):
            view_range = self.plot_widget.viewRange()
            mid_x = view_range[0][0]
            mid_y = item.value()
            color = item.pen.color().name()
        else:
            return
        label = pg.TextItem(text=text, color=color)
        label.setPos(mid_x, mid_y)
        self.plot_widget.addItem(label)
        self.item_labels[item] = label
    def delete_item(self, item):
        if item in self.drawing_items:
            # Store the item and its label for undo functionality
            deleted_info = {'item': item, 'label': None}
            if item in self.item_labels:
                deleted_info['label'] = self.item_labels[item]
                self.plot_widget.removeItem(self.item_labels[item])
                del self.item_labels[item]

            # Add to deleted items stack
            self.deleted_items.append(deleted_info)

            # Clear redo stack when a new deletion occurs
            self.redone_items.clear()

            # Remove from drawing items
            self.plot_widget.removeItem(item)
            self.drawing_items.remove(item)

        self.selected_item = None
    def clear_all(self):
        # Store all items for potential undo
        deleted_batch = []
        for item in self.drawing_items:
            deleted_info = {'item': item, 'label': None}
            if item in self.item_labels:
                deleted_info['label'] = self.item_labels[item]
                self.plot_widget.removeItem(self.item_labels[item])
            self.plot_widget.removeItem(item)
            deleted_batch.append(deleted_info)

        # Add the batch as a single undo operation
        if deleted_batch:
            self.deleted_items.append(deleted_batch)

        # Clear redo stack
        self.redone_items.clear()

        self.drawing_items.clear()
        self.item_labels.clear()
        self.current_item = None
        self.start_pos = None
        self.drawing = False
        self.selected_item = None

    def undo(self):
        """Undo the last drawing operation"""
        if not self.deleted_items:
            return

        last_deleted = self.deleted_items.pop()

        # Check if it's a batch deletion (from clear_all)
        if isinstance(last_deleted, list):
            restored_batch = []
            for deleted_info in last_deleted:
                item = deleted_info['item']
                label = deleted_info['label']

                # Restore the item
                self.plot_widget.addItem(item)
                self.drawing_items.append(item)

                # Restore the label if it exists
                if label:
                    self.plot_widget.addItem(label)
                    self.item_labels[item] = label

                restored_batch.append(deleted_info)

            # Add to redo stack as a batch
            self.redone_items.append(restored_batch)
        else:
            # Single item deletion
            item = last_deleted['item']
            label = last_deleted['label']

            # Restore the item
            self.plot_widget.addItem(item)
            self.drawing_items.append(item)

            # Restore the label if it exists
            if label:
                self.plot_widget.addItem(label)
                self.item_labels[item] = label

            # Add to redo stack
            self.redone_items.append(last_deleted)

    def redo(self):
        """Redo the last undone drawing operation"""
        if not self.redone_items:
            return

        last_redone = self.redone_items.pop()

        # Check if it's a batch operation
        if isinstance(last_redone, list):
            deleted_batch = []
            for redone_info in last_redone:
                item = redone_info['item']
                label = redone_info['label']

                # Remove the item
                self.plot_widget.removeItem(item)
                self.drawing_items.remove(item)

                # Remove the label if it exists
                if label:
                    self.plot_widget.removeItem(label)
                    if item in self.item_labels:
                        del self.item_labels[item]

                deleted_batch.append(redone_info)

            # Add to deleted items stack as a batch
            self.deleted_items.append(deleted_batch)
        else:
            # Single item operation
            item = last_redone['item']
            label = last_redone['label']

            # Remove the item
            self.plot_widget.removeItem(item)
            self.drawing_items.remove(item)

            # Remove the label if it exists
            if label:
                self.plot_widget.removeItem(label)
                if item in self.item_labels:
                    del self.item_labels[item]

            # Add to deleted items stack
            self.deleted_items.append(last_redone)

    def add_anchored_text(self, pos, text):
        """Add text that remains anchored to the chart even when scrolling"""
        # Create a text item with a background for better visibility
        html_text = f'<div style="background-color: rgba(0,0,0,0.7); padding: 5px; border-radius: 5px; color: white;">{text}</div>'
        text_item = pg.TextItem(html=html_text, anchor=(0.5, 0.5))
        text_item.setPos(pos.x(), pos.y())

        # Add to plot and track as anchored text
        self.plot_widget.addItem(text_item)
        self.drawing_items.append(text_item)
        self.anchored_texts.append(text_item)

        # Clear redo stack when a new item is added
        self.redone_items.clear()

        return text_item
    def update_drawing(self, pos):
        if not self.drawing or not self.current_item:
            return
        if self.current_tool == 'line':
            self.current_item.setData([self.start_pos.x(), pos.x()], [self.start_pos.y(), pos.y()])
        elif self.current_tool == 'rectangle':
            x = [self.start_pos.x(), self.start_pos.x(), pos.x(), pos.x(), self.start_pos.x()]
            y = [self.start_pos.y(), pos.y(), pos.y(), self.start_pos.y(), self.start_pos.y()]
            self.current_item.setData(x, y)
        elif self.current_tool == 'horizontal':
            self.current_item.setPos(pos.y())
        elif self.current_tool == 'vertical':
            self.current_item.setPos(pos.x())
        elif self.current_tool == 'text':
            self.current_item.setPos(pos.x(), pos.y())
    def finish_drawing(self, pos=None):
        if self.current_item:
            if pos:
                self.update_drawing(pos)
            self.drawing_items.append(self.current_item)
        self.current_item = None
        self.start_pos = None
        self.drawing = False
    def add_text(self, pos, text):
        text_item = pg.TextItem(text=text, color='w')
        text_item.setPos(pos.x(), pos.y())
        self.plot_widget.addItem(text_item)
        self.drawing_items.append(text_item)
    def select_item(self, pos):
        for item in self.drawing_items:
            if isinstance(item, pg.PlotDataItem):
                data = item.getData()
                if data[0] is not None:
                    x, y = data
                    for i in range(len(x)-1):
                        if self._point_near_line(pos, QtCore.QPointF(x[i], y[i]), QtCore.QPointF(x[i+1], y[i+1])):
                            self.selected_item = item
                            return True
            elif isinstance(item, pg.InfiniteLine):
                if abs(item.value() - (pos.y() if item.angle == 0 else pos.x())) < 5:
                    self.selected_item = item
                    return True
            elif isinstance(item, pg.TextItem):
                item_pos = item.pos()
                if abs(pos.x() - item_pos.x()) < 20 and abs(pos.y() - item_pos.y()) < 20:
                    self.selected_item = item
                    return True
        return False
    def _point_near_line(self, point, line_start, line_end, threshold=5):
        line_vec = line_end - line_start
        point_vec = QtCore.QPointF(point.x() - line_start.x(), point.y() - line_start.y())
        line_len = (line_vec.x()**2 + line_vec.y()**2)**0.5
        if line_len == 0:
            return False
        t = max(0, min(1, (point_vec.x()*line_vec.x() + point_vec.y()*line_vec.y()) / line_len**2))
        proj = QtCore.QPointF(
            line_start.x() + t * line_vec.x(),
            line_start.y() + t * line_vec.y()
        )
        dist = ((point.x() - proj.x())**2 + (point.y() - proj.y())**2)**0.5
        return dist < threshold

# Import statements moved to the top of the file

# A modern flat SVG check icon – 20x20 – rendered as a white checkmark.
# You can generate a custom SVG and encode it to base64 if desired.
CHECKMARK_SVG_BASE64 = (
    "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLThCJz8+PHN2ZyB3aWR0aD0iMjAiIGhlaWdo"
    "dD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3Zn"
    "Ij48cG9seWxpbmUgcG9pbnRzPSI0LDEwIDgsMTQgMTYsNiIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0"
    "ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+"
)

STYLE_SHEET = f"""
/* Overall Dialog & Widgets */
QDialog {{
    background-color: #2b2b2b;
    color: #e0e0e0;
    font-family: "Segoe UI", sans-serif;
}}

QGroupBox {{
    border: 1px solid #555;
    border-radius: 6px;
    padding: 8px;
    margin-top: 10px;
}}
QGroupBox::title {{
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    font-size: 10pt;
    font-weight: bold;
}}

QLabel {{
    font-size: 10pt;
}}

/* Buttons */
QPushButton {{
    background-color: #4a4a4a;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
}}
QPushButton:hover {{
    background-color: #5a5a5a;
}}

/* Input Fields */
QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
    background-color: #3a3a3a;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 3px;
}}

/* Scroll area */
QScrollArea {{
    border: none;
}}

/* ---------- State-of-the-Art Checkbox Style ---------- */
QCheckBox {{
    spacing: 8px;
    font-size: 10pt;
}}

QCheckBox::indicator {{
    width: 20px;
    height: 20px;
    border: 2px solid #000000;
    border-radius: 4px;
    background-color: #808080;
}}

QCheckBox::indicator:unchecked:hover {{
    border: 2px solid #000000;
    background-color: #909090;
}}

QCheckBox::indicator:checked {{
    background-color: #FFFFFF;
    border: 2px solid #000000;
    image: url("data:image/svg+xml;base64,{CHECKMARK_SVG_BASE64}");
}}

QCheckBox::indicator:checked:hover {{
    background-color: #F0F0F0;
    border: 2px solid #000000;
}}

/* ---------- State-of-the-Art Radio Button Style ---------- */
QRadioButton {{
    spacing: 8px;
    font-size: 10pt;
}}

QRadioButton::indicator {{
    width: 20px;
    height: 20px;
    border: 2px solid #000000;
    border-radius: 10px;
    background-color: #808080;
}}

QRadioButton::indicator:unchecked:hover {{
    border: 2px solid #000000;
    background-color: #909090;
}}

QRadioButton::indicator:checked {{
    background-color: #FFFFFF;
    border: 2px solid #000000;
    background-image: radial-gradient(circle, #000000 30%, transparent 30%);
}}

QRadioButton::indicator:checked:hover {{
    background-color: #F0F0F0;
    border: 2px solid #000000;
    background-image: radial-gradient(circle, #000000 30%, transparent 30%);
}}
"""

from flow_layout import FlowLayout



class SettingsDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(STYLE_SHEET)
        self.setWindowTitle("Settings")
        # Make the dialog resizable
        self.setWindowFlags(self.windowFlags() | QtCore.Qt.WindowType.WindowMaximizeButtonHint | QtCore.Qt.WindowType.WindowMinimizeButtonHint)
        # Default colors for Appearance settings:
        self.default_colors = {
            'background': '#1e1e1e',
            'bullish': '#00ff00',
            'bearish': '#ff0000',
            'vector': '#2196F3'
        }
        self.initUI()

    def initUI(self):
        main_layout = QtWidgets.QVBoxLayout(self)

        # Set dialog to be resizable without minimum size restrictions

        # Load saved settings if available
        self.load_settings()

        # Create tab widget for different settings categories
        tab_widget = QtWidgets.QTabWidget()

        # Tab 1: Appearance
        appearance_tab = QtWidgets.QWidget()
        appearance_layout = QtWidgets.QVBoxLayout(appearance_tab)

        # Create color settings group
        color_group = QtWidgets.QGroupBox("Chart Colors")
        color_layout = QtWidgets.QFormLayout(color_group)

        # Create color buttons
        self.bg_color_btn = self.createColorButton(self.default_colors['background'])
        self.bull_color_btn = self.createColorButton(self.default_colors['bullish'])
        self.bear_color_btn = self.createColorButton(self.default_colors['bearish'])
        self.vector_color_btn = self.createColorButton(self.default_colors['vector'])

        # Add color buttons to layout
        color_layout.addRow("Background Color:", self.bg_color_btn)
        color_layout.addRow("Bullish Color:", self.bull_color_btn)
        color_layout.addRow("Bearish Color:", self.bear_color_btn)
        color_layout.addRow("Vector Color:", self.vector_color_btn)

        # Add reset button
        reset_btn = QtWidgets.QPushButton("Reset Colors")
        reset_btn.clicked.connect(self.reset_colors)
        color_layout.addRow(reset_btn)

        # Add color group to appearance tab
        appearance_layout.addWidget(color_group)

        # Add Auto Fetch checkbox to Chart Appearance
        auto_fetch_group = QtWidgets.QGroupBox("Data Settings")
        auto_fetch_layout = QtWidgets.QVBoxLayout(auto_fetch_group)
        self.auto_fetch_check = QtWidgets.QCheckBox("Auto-Fetch Data")
        self.auto_fetch_check.setChecked(False)
        self.auto_fetch_check.setToolTip("Automatically fetch new data every 60 seconds")
        auto_fetch_layout.addWidget(self.auto_fetch_check)

        # Add Trading Hours section
        trading_hours_label = QtWidgets.QLabel("Trading Hours:")
        trading_hours_label.setStyleSheet("color: #e0e0e0; font-weight: bold; margin-top: 10px;")
        auto_fetch_layout.addWidget(trading_hours_label)

        # Create radio buttons for RTH/ETH selection
        self.trading_hours_group = QtWidgets.QButtonGroup()

        self.rth_radio = QtWidgets.QRadioButton("RTH Only")
        self.rth_radio.setStyleSheet("color: #e0e0e0; margin-left: 10px;")
        self.rth_radio.setToolTip("Show Regular Trading Hours data only (9:30 AM - 4:00 PM ET)")

        self.eth_radio = QtWidgets.QRadioButton("ETH Only")
        self.eth_radio.setStyleSheet("color: #e0e0e0; margin-left: 10px;")
        self.eth_radio.setToolTip("Show Extended Trading Hours data only (pre-market and after-hours)")

        self.both_radio = QtWidgets.QRadioButton("Both")
        self.both_radio.setStyleSheet("color: #e0e0e0; margin-left: 10px;")
        self.both_radio.setToolTip("Show both Regular and Extended Trading Hours data")
        self.both_radio.setChecked(True)  # Default to showing both

        # Add radio buttons to group and layout
        self.trading_hours_group.addButton(self.rth_radio, 0)
        self.trading_hours_group.addButton(self.eth_radio, 1)
        self.trading_hours_group.addButton(self.both_radio, 2)

        auto_fetch_layout.addWidget(self.rth_radio)
        auto_fetch_layout.addWidget(self.eth_radio)
        auto_fetch_layout.addWidget(self.both_radio)

        appearance_layout.addWidget(auto_fetch_group)

        appearance_layout.addStretch()

        # Tab 2: Visuals
        visuals_tab = QtWidgets.QWidget()
        visuals_layout = QtWidgets.QVBoxLayout(visuals_tab)

        # Section 1: Visual Elements
        visual_elements_group = QtWidgets.QGroupBox("Visual Elements")
        visual_elements_layout = QtWidgets.QVBoxLayout(visual_elements_group)

        self.show_candlesticks_check = QtWidgets.QCheckBox("Show Candlesticks")
        self.show_candlesticks_check.setChecked(True)
        self.show_candlesticks_check.setToolTip("Show candlesticks on the chart")

        self.show_vectors_check = QtWidgets.QCheckBox("Show Vectors")
        self.show_vectors_check.setChecked(True)
        self.show_vectors_check.setToolTip("Show vector lines on the chart")

        self.show_probability_bands_check = QtWidgets.QCheckBox("Show Probability Bands")
        self.show_probability_bands_check.setChecked(True)
        self.show_probability_bands_check.setToolTip("Show probability bands on the chart")

        self.high_trend_check = QtWidgets.QCheckBox("High Trend")
        self.high_trend_check.setChecked(True)
        self.high_trend_check.setToolTip("Show high trend indicator at the bottom of the chart")

        visual_elements_layout.addWidget(self.show_candlesticks_check)
        visual_elements_layout.addWidget(self.show_vectors_check)
        visual_elements_layout.addWidget(self.show_probability_bands_check)
        visual_elements_layout.addWidget(self.high_trend_check)
        visuals_layout.addWidget(visual_elements_group)

        # Section 2: Chart Analysis
        chart_analysis_group = QtWidgets.QGroupBox("Chart Analysis")
        chart_analysis_layout = QtWidgets.QVBoxLayout(chart_analysis_group)

        self.show_peak_trough_rays_check = QtWidgets.QCheckBox("Show Peak/Trough Rays")
        self.show_peak_trough_rays_check.setChecked(True)
        self.show_peak_trough_rays_check.setToolTip("Show peak and trough rays on the chart")

        self.show_average_cycle_check = QtWidgets.QCheckBox("Show Average Cycle")
        self.show_average_cycle_check.setChecked(True)
        self.show_average_cycle_check.setToolTip("Show average cycle based on average peak/trough patterns")

        self.show_imprints_check = QtWidgets.QCheckBox("Show P/T")
        self.show_imprints_check.setChecked(True)
        self.show_imprints_check.setToolTip("Show P/T on the chart")

        self.show_clusters_check = QtWidgets.QCheckBox("Show Clusters")
        self.show_clusters_check.setChecked(True)
        self.show_clusters_check.setToolTip("Show clusters on the chart")

        # Density visualization checkboxes
        self.show_density_heatmap_check = QtWidgets.QCheckBox("Show Density Heatmap")
        self.show_density_heatmap_check.setChecked(False)
        self.show_density_heatmap_check.setToolTip("Show density heatmap visualization on the chart")

        self.show_density_profile_check = QtWidgets.QCheckBox("Show Density Profile")
        self.show_density_profile_check.setChecked(False)
        self.show_density_profile_check.setToolTip("Show density profile visualization on the chart")

        # Use Full Candle Length checkbox for density calculation
        self.use_full_candle_length_check = QtWidgets.QCheckBox("Use Full Candle Length")
        self.use_full_candle_length_check.setChecked(False)
        self.use_full_candle_length_check.setToolTip("Use full candle length for density calculation (default: use current sampling method)")

        # Density bins setting
        density_bins_layout = QtWidgets.QHBoxLayout()
        density_bins_label = QtWidgets.QLabel("Density Bins:")
        density_bins_label.setStyleSheet("color: #e0e0e0;")
        self.density_bins_spin = QtWidgets.QSpinBox()
        self.density_bins_spin.setRange(10, 200)
        self.density_bins_spin.setValue(50)  # Default value
        self.density_bins_spin.setToolTip("Number of bins for density calculation (more bins = finer detail)")
        self.density_bins_spin.setStyleSheet("color: #e0e0e0; background-color: #3a3a3a; border: 1px solid #555; border-radius: 4px; padding: 3px;")
        density_bins_layout.addWidget(density_bins_label)
        density_bins_layout.addWidget(self.density_bins_spin)
        density_bins_layout.addStretch()

        chart_analysis_layout.addWidget(self.show_peak_trough_rays_check)
        chart_analysis_layout.addWidget(self.show_average_cycle_check)
        chart_analysis_layout.addWidget(self.show_imprints_check)
        chart_analysis_layout.addWidget(self.show_clusters_check)
        chart_analysis_layout.addWidget(self.show_density_heatmap_check)
        chart_analysis_layout.addWidget(self.show_density_profile_check)
        chart_analysis_layout.addWidget(self.use_full_candle_length_check)
        chart_analysis_layout.addLayout(density_bins_layout)
        visuals_layout.addWidget(chart_analysis_group)



        visuals_layout.addStretch()

        # Tab 3: Performance
        performance_tab = QtWidgets.QWidget()
        performance_layout = QtWidgets.QVBoxLayout(performance_tab)

        # Performance Settings
        performance_group = QtWidgets.QGroupBox("Performance Settings")
        performance_settings_layout = QtWidgets.QVBoxLayout(performance_group)

        # Performance Mode
        self.performance_mode_check = QtWidgets.QCheckBox("Enable Performance Mode")
        self.performance_mode_check.setChecked(False)
        self.performance_mode_check.setToolTip("Optimize rendering for better performance (reduces visual quality)")
        performance_settings_layout.addWidget(self.performance_mode_check)

        # Simplified P/T
        self.simplified_imprints_check = QtWidgets.QCheckBox("Use Simplified P/T")
        self.simplified_imprints_check.setChecked(False)
        self.simplified_imprints_check.setToolTip("Use a simplified rendering method for P/T to improve performance")
        performance_settings_layout.addWidget(self.simplified_imprints_check)

        # Hide P/T Labels
        self.hide_imprint_labels_check = QtWidgets.QCheckBox("Hide P/T Labels")
        self.hide_imprint_labels_check.setChecked(False)
        self.hide_imprint_labels_check.setToolTip("Hide all P/T labels to improve performance")
        performance_settings_layout.addWidget(self.hide_imprint_labels_check)

        # Background Rendering
        self.background_rendering_check = QtWidgets.QCheckBox("Enable Background Rendering")
        self.background_rendering_check.setChecked(False)
        self.background_rendering_check.setToolTip("Render P/T in a background thread to keep UI responsive")
        performance_settings_layout.addWidget(self.background_rendering_check)

        # P/T Cache Settings
        cache_group = QtWidgets.QGroupBox("P/T Cache Settings")
        cache_layout = QtWidgets.QGridLayout(cache_group)

        cache_layout.addWidget(QtWidgets.QLabel("P/T Cache Size:"), 0, 0)
        self.imprint_cache_size_spin = QtWidgets.QSpinBox()
        self.imprint_cache_size_spin.setRange(10, 1000)
        self.imprint_cache_size_spin.setValue(100)
        self.imprint_cache_size_spin.setSingleStep(10)
        self.imprint_cache_size_spin.setToolTip("Number of P/T sets to cache (higher values use more memory)")
        cache_layout.addWidget(self.imprint_cache_size_spin, 0, 1)

        # Batch Rendering Settings
        batch_group = QtWidgets.QGroupBox("Batch Rendering Settings")
        batch_layout = QtWidgets.QGridLayout(batch_group)

        batch_layout.addWidget(QtWidgets.QLabel("Batch Size:"), 0, 0)
        self.batch_size_spin = QtWidgets.QSpinBox()
        self.batch_size_spin.setRange(5, 100)
        self.batch_size_spin.setValue(20)
        self.batch_size_spin.setSingleStep(5)
        self.batch_size_spin.setToolTip("Number of P/T to render in each batch (lower values improve responsiveness)")
        batch_layout.addWidget(self.batch_size_spin, 0, 1)

        # Add groups to performance tab
        performance_layout.addWidget(performance_group)
        performance_layout.addWidget(cache_group)
        performance_layout.addWidget(batch_group)
        performance_layout.addStretch()

        # Tab 4: Clusters
        clusters_tab = QtWidgets.QWidget()
        clusters_layout = QtWidgets.QVBoxLayout(clusters_tab)

        # Cluster Settings
        group_cluster = QtWidgets.QGroupBox("Cluster Settings")
        cluster_layout = QtWidgets.QGridLayout(group_cluster)

        self.enable_clusters = QtWidgets.QCheckBox("Enable Clusters")
        self.enable_clusters.setChecked(True)
        cluster_layout.addWidget(self.enable_clusters, 0, 0, 1, 2)

        cluster_layout.addWidget(QtWidgets.QLabel("Cluster Threshold %:"), 1, 0)
        self.cluster_threshold_spin = QtWidgets.QDoubleSpinBox()
        self.cluster_threshold_spin.setRange(0.01, 1.0)
        self.cluster_threshold_spin.setValue(0.15)
        self.cluster_threshold_spin.setSingleStep(0.01)
        self.cluster_threshold_spin.setDecimals(2)
        cluster_layout.addWidget(self.cluster_threshold_spin, 1, 1)

        cluster_layout.addWidget(QtWidgets.QLabel("Min Cluster Size:"), 2, 0)
        self.min_cluster_size_spin = QtWidgets.QSpinBox()
        self.min_cluster_size_spin.setRange(2, 10)
        self.min_cluster_size_spin.setValue(3)
        cluster_layout.addWidget(self.min_cluster_size_spin, 2, 1)

        cluster_layout.addWidget(QtWidgets.QLabel("Cluster Color:"), 3, 0)
        self.cluster_color_combo = QtWidgets.QComboBox()
        self.cluster_color_combo.addItems(["yellow", "red", "green", "blue", "cyan", "magenta"])
        self.cluster_color_combo.setCurrentText("yellow")
        cluster_layout.addWidget(self.cluster_color_combo, 3, 1)

        cluster_layout.addWidget(QtWidgets.QLabel("Cluster Opacity:"), 4, 0)
        self.cluster_opacity_spin = QtWidgets.QSpinBox()
        self.cluster_opacity_spin.setRange(10, 100)
        self.cluster_opacity_spin.setValue(50)
        self.cluster_opacity_spin.setSuffix("%")
        cluster_layout.addWidget(self.cluster_opacity_spin, 4, 1)

        clusters_layout.addWidget(group_cluster)
        clusters_layout.addStretch()

        # Add tabs to tab widget
        tab_widget.addTab(appearance_tab, "Appearance")
        tab_widget.addTab(visuals_tab, "Visuals")
        tab_widget.addTab(performance_tab, "Performance")
        tab_widget.addTab(clusters_tab, "Clusters")

        # Add tab widget to main layout
        main_layout.addWidget(tab_widget)


        # Create hidden variables to maintain compatibility with existing code
        self.model_type_combo = QtWidgets.QComboBox()
        self.model_type_combo.addItems(["auto", "random_forest", "gradient_boosting", "hybrid_forest", "xgboost", "lightgbm"])
        self.model_type_combo.setCurrentText("auto")
        self.model_type_combo.setVisible(False)

        self.lookback_spin = QtWidgets.QSpinBox()
        self.lookback_spin.setRange(1, 30)
        self.lookback_spin.setValue(5)
        self.lookback_spin.setVisible(False)

        self.lookahead_spin = QtWidgets.QSpinBox()
        self.lookahead_spin.setRange(1, 30)
        self.lookahead_spin.setValue(10)
        self.lookahead_spin.setVisible(False)

        self.reversal_threshold_spin = QtWidgets.QDoubleSpinBox()
        self.reversal_threshold_spin.setRange(0.1, 5.0)
        self.reversal_threshold_spin.setValue(0.5)
        self.reversal_threshold_spin.setVisible(False)

        self.confidence_threshold_spin = QtWidgets.QDoubleSpinBox()
        self.confidence_threshold_spin.setRange(0.01, 0.99)
        self.confidence_threshold_spin.setValue(0.65)
        self.confidence_threshold_spin.setVisible(False)

        self.enable_classifier = QtWidgets.QCheckBox()
        self.enable_classifier.setChecked(True)
        self.enable_classifier.setVisible(False)

        self.enable_calibration = QtWidgets.QCheckBox()
        self.enable_calibration.setChecked(False)
        self.enable_calibration.setVisible(False)

        self.auto_train_classifier = QtWidgets.QCheckBox()
        self.auto_train_classifier.setChecked(True)
        self.auto_train_classifier.setVisible(False)

        self.use_pivot_validator = QtWidgets.QCheckBox()
        self.use_pivot_validator.setChecked(False)
        self.use_pivot_validator.setVisible(False)

        self.adaptive_cache_check = QtWidgets.QCheckBox()
        self.adaptive_cache_check.setChecked(True)
        self.adaptive_cache_check.setVisible(False)

        self.cache_size_spin = QtWidgets.QSpinBox()
        self.cache_size_spin.setRange(100, 50000)
        self.cache_size_spin.setValue(1000)
        self.cache_size_spin.setVisible(False)

        self.min_cache_size_spin = QtWidgets.QSpinBox()
        self.min_cache_size_spin.setRange(50, 1000)
        self.min_cache_size_spin.setValue(100)
        self.min_cache_size_spin.setVisible(False)

        self.max_cache_size_spin = QtWidgets.QSpinBox()
        self.max_cache_size_spin.setRange(1000, 100000)
        self.max_cache_size_spin.setValue(10000)
        self.max_cache_size_spin.setVisible(False)

        self.confirmation_period_spin = QtWidgets.QSpinBox()
        self.confirmation_period_spin.setRange(1, 10)
        self.confirmation_period_spin.setValue(3)
        self.confirmation_period_spin.setVisible(False)

        self.min_crossing_magnitude_spin = QtWidgets.QDoubleSpinBox()
        self.min_crossing_magnitude_spin.setRange(0.01, 1.0)
        self.min_crossing_magnitude_spin.setValue(0.05)
        self.min_crossing_magnitude_spin.setVisible(False)

        self.use_rsi_filter = QtWidgets.QCheckBox()
        self.use_rsi_filter.setChecked(True)
        self.use_rsi_filter.setVisible(False)

        self.rsi_overbought_spin = QtWidgets.QSpinBox()
        self.rsi_overbought_spin.setRange(50, 90)
        self.rsi_overbought_spin.setValue(60)
        self.rsi_overbought_spin.setVisible(False)

        self.rsi_oversold_spin = QtWidgets.QSpinBox()
        self.rsi_oversold_spin.setRange(10, 50)
        self.rsi_oversold_spin.setValue(40)
        self.rsi_oversold_spin.setVisible(False)

        self.use_volatility_adjustment = QtWidgets.QCheckBox()
        self.use_volatility_adjustment.setChecked(False)
        self.use_volatility_adjustment.setVisible(False)

        self.use_high_low_constraint = QtWidgets.QCheckBox()
        self.use_high_low_constraint.setChecked(False)
        self.use_high_low_constraint.setVisible(False)

        self.use_probabilistic_filtering = QtWidgets.QCheckBox()
        self.use_probabilistic_filtering.setChecked(False)
        self.use_probabilistic_filtering.setVisible(False)

        self.enable_signal_filter = QtWidgets.QCheckBox()
        self.enable_signal_filter.setChecked(True)
        self.enable_signal_filter.setVisible(False)

        self.enable_pivot_constraint = QtWidgets.QCheckBox()
        self.enable_pivot_constraint.setChecked(True)
        self.enable_pivot_constraint.setVisible(False)

        # Cluster settings have been moved to the Clusters tab

        # ----- Bottom Button Row -----
        bottom_layout = QtWidgets.QHBoxLayout()
        bottom_layout.addStretch()



        # Add Save Settings button
        save_settings_btn = QtWidgets.QPushButton("Save Settings")
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #007ACC;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0098FF;
            }
        """)
        save_settings_btn.clicked.connect(self.save_settings)
        bottom_layout.addWidget(save_settings_btn)

        ok_btn = QtWidgets.QPushButton("OK")
        ok_btn.clicked.connect(self.on_ok_clicked)
        cancel_btn = QtWidgets.QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        bottom_layout.addWidget(ok_btn)
        bottom_layout.addWidget(cancel_btn)
        main_layout.addLayout(bottom_layout)

    def createColorButton(self, initial_color):
        btn = QtWidgets.QPushButton()
        btn.setFixedSize(100, 30)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {initial_color};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        btn.clicked.connect(lambda: self.choose_color(btn))
        return btn

    def choose_color(self, button):
        current_color = button.styleSheet().split("background-color:")[1].split(";")[0].strip()
        color = QtWidgets.QColorDialog.getColor(QtGui.QColor(current_color), self)
        if color.isValid():
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color.name()};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)



    def get_display_options(self):
        """Return a dictionary of display options for visual elements"""
        return {
            'show_candlesticks': self.show_candlesticks_check.isChecked() if hasattr(self, 'show_candlesticks_check') else True,
            'show_vector': self.show_vectors_check.isChecked(),
            'show_peak_trough_rays': self.show_peak_trough_rays_check.isChecked(),
            'show_average_cycle': self.show_average_cycle_check.isChecked(),
            'show_probability_bands': self.show_probability_bands_check.isChecked(),
            'show_imprints': self.show_imprints_check.isChecked(),
            'show_clusters': self.show_clusters_check.isChecked(),
            'show_density_heatmap': self.show_density_heatmap_check.isChecked() if hasattr(self, 'show_density_heatmap_check') else False,
            'high_trend': self.high_trend_check.isChecked(),
            'show_density_profile': self.show_density_profile_check.isChecked() if hasattr(self, 'show_density_profile_check') else False,
            'use_full_candle_length': self.use_full_candle_length_check.isChecked() if hasattr(self, 'use_full_candle_length_check') else False,
            'density_bins': self.density_bins_spin.value() if hasattr(self, 'density_bins_spin') else 50,
            'performance_mode': self.performance_mode_check.isChecked() if hasattr(self, 'performance_mode_check') else False,
            'simplified_imprints': self.simplified_imprints_check.isChecked() if hasattr(self, 'simplified_imprints_check') else False,
            'hide_imprint_labels': self.hide_imprint_labels_check.isChecked() if hasattr(self, 'hide_imprint_labels_check') else False,
            'background_rendering': self.background_rendering_check.isChecked() if hasattr(self, 'background_rendering_check') else False,
            'batch_size': self.batch_size_spin.value() if hasattr(self, 'batch_size_spin') else 20
        }

    def get_chart_colors(self):
        return {
            'background': self.bg_color_btn.styleSheet().split("background-color:")[1].split(";")[0].strip(),
            'bullish': self.bull_color_btn.styleSheet().split("background-color:")[1].split(";")[0].strip(),
            'bearish': self.bear_color_btn.styleSheet().split("background-color:")[1].split(";")[0].strip(),
            'vector': self.vector_color_btn.styleSheet().split("background-color:")[1].split(";")[0].strip()
        }

    def reset_colors(self):
        self.bg_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['background']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.bull_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['bullish']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.bear_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['bearish']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.vector_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['vector']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)


    def load_settings(self):
        """Load settings from QSettings"""
        settings = QtCore.QSettings('MarketOddsApp', 'MarketOddsSettings')

        # Load window size and position if previously saved
        if settings.contains('settings_dialog/size'):
            self.resize(settings.value('settings_dialog/size'))
        if settings.contains('settings_dialog/pos'):
            self.move(settings.value('settings_dialog/pos'))

        # Load trading hours setting
        if hasattr(self, 'trading_hours_group') and settings.contains('trading_hours'):
            trading_hours = settings.value('trading_hours', 'both')
            if trading_hours == 'rth':
                self.rth_radio.setChecked(True)
            elif trading_hours == 'eth':
                self.eth_radio.setChecked(True)
            else:
                self.both_radio.setChecked(True)

        # Load display options
        if hasattr(self, 'show_candlesticks_check') and settings.contains('display_option_show_candlesticks'):
            self.show_candlesticks_check.setChecked(settings.value('display_option_show_candlesticks', type=bool))
        if hasattr(self, 'show_average_cycle_check') and settings.contains('display_option_show_average_cycle'):
            self.show_average_cycle_check.setChecked(settings.value('display_option_show_average_cycle', type=bool))
        if hasattr(self, 'high_trend_check') and settings.contains('display_option_high_trend'):
            self.high_trend_check.setChecked(settings.value('display_option_high_trend', type=bool))

        # Load density settings
        if hasattr(self, 'show_density_heatmap_check') and settings.contains('display_option_show_density_heatmap'):
            self.show_density_heatmap_check.setChecked(settings.value('display_option_show_density_heatmap', type=bool))
        if hasattr(self, 'show_density_profile_check') and settings.contains('display_option_show_density_profile'):
            self.show_density_profile_check.setChecked(settings.value('display_option_show_density_profile', type=bool))
        if hasattr(self, 'use_full_candle_length_check') and settings.contains('display_option_use_full_candle_length'):
            self.use_full_candle_length_check.setChecked(settings.value('display_option_use_full_candle_length', type=bool))
        if hasattr(self, 'density_bins_spin') and settings.contains('display_option_density_bins'):
            self.density_bins_spin.setValue(settings.value('display_option_density_bins', type=int))

        # Load performance settings
        if hasattr(self, 'performance_mode_check') and settings.contains('performance_mode'):
            self.performance_mode_check.setChecked(settings.value('performance_mode', type=bool))
        if hasattr(self, 'simplified_imprints_check') and settings.contains('simplified_imprints'):
            self.simplified_imprints_check.setChecked(settings.value('simplified_imprints', type=bool))
        if hasattr(self, 'hide_imprint_labels_check') and settings.contains('hide_imprint_labels'):
            self.hide_imprint_labels_check.setChecked(settings.value('hide_imprint_labels', type=bool))
        if hasattr(self, 'background_rendering_check') and settings.contains('background_rendering'):
            self.background_rendering_check.setChecked(settings.value('background_rendering', type=bool))
        if hasattr(self, 'imprint_cache_size_spin') and settings.contains('imprint_cache_size'):
            self.imprint_cache_size_spin.setValue(settings.value('imprint_cache_size', type=int))
        if hasattr(self, 'batch_size_spin') and settings.contains('batch_size'):
            self.batch_size_spin.setValue(settings.value('batch_size', type=int))

    def save_settings(self):
        """Save all settings to QSettings"""
        settings = QtCore.QSettings('MarketOddsApp', 'MarketOddsSettings')

        # Save dialog size and position
        settings.setValue('settings_dialog/size', self.size())
        settings.setValue('settings_dialog/pos', self.pos())

        # Save chart colors
        for key, value in self.get_chart_colors().items():
            settings.setValue(f'chart_color_{key}', value)

        # Save trading hours setting
        if hasattr(self, 'trading_hours_group'):
            if self.rth_radio.isChecked():
                settings.setValue('trading_hours', 'rth')
            elif self.eth_radio.isChecked():
                settings.setValue('trading_hours', 'eth')
            else:
                settings.setValue('trading_hours', 'both')

        # Save display options
        for key, value in self.get_display_options().items():
            settings.setValue(f'display_option_{key}', value)



        # Save classifier parameters
        settings.setValue('classifier_model_type', self.model_type_combo.currentText())
        settings.setValue('classifier_lookback', self.lookback_spin.value())
        settings.setValue('classifier_lookahead', self.lookahead_spin.value())
        settings.setValue('classifier_reversal_threshold', self.reversal_threshold_spin.value())
        settings.setValue('classifier_confidence_threshold', self.confidence_threshold_spin.value())
        settings.setValue('enable_classifier', self.enable_classifier.isChecked())
        settings.setValue('enable_calibration', self.enable_calibration.isChecked())
        settings.setValue('auto_train_classifier', self.auto_train_classifier.isChecked())
        settings.setValue('use_pivot_validator', self.use_pivot_validator.isChecked())
        settings.setValue('adaptive_cache', self.adaptive_cache_check.isChecked())
        settings.setValue('cache_size', self.cache_size_spin.value())
        settings.setValue('min_cache_size', self.min_cache_size_spin.value())
        settings.setValue('max_cache_size', self.max_cache_size_spin.value())

        # Save signal filter settings
        settings.setValue('confirmation_period', self.confirmation_period_spin.value())
        settings.setValue('min_crossing_magnitude', self.min_crossing_magnitude_spin.value())
        settings.setValue('use_rsi_filter', self.use_rsi_filter.isChecked())
        settings.setValue('rsi_overbought', self.rsi_overbought_spin.value())
        settings.setValue('rsi_oversold', self.rsi_oversold_spin.value())
        settings.setValue('use_volatility_adjustment', self.use_volatility_adjustment.isChecked())
        settings.setValue('use_high_low_constraint', self.use_high_low_constraint.isChecked())
        settings.setValue('use_probabilistic_filtering', self.use_probabilistic_filtering.isChecked())
        settings.setValue('confidence_threshold', self.confidence_threshold_spin.value())
        settings.setValue('enable_signal_filter', self.enable_signal_filter.isChecked())
        settings.setValue('enable_pivot_constraint', self.enable_pivot_constraint.isChecked())

        # Save cluster settings
        settings.setValue('enable_clusters', self.enable_clusters.isChecked())
        settings.setValue('cluster_threshold', self.cluster_threshold_spin.value())
        settings.setValue('min_cluster_size', self.min_cluster_size_spin.value())
        settings.setValue('cluster_color', self.cluster_color_combo.currentText())
        settings.setValue('cluster_opacity', self.cluster_opacity_spin.value())

        # Save performance settings
        if hasattr(self, 'performance_mode_check'):
            settings.setValue('performance_mode', self.performance_mode_check.isChecked())
        if hasattr(self, 'simplified_imprints_check'):
            settings.setValue('simplified_imprints', self.simplified_imprints_check.isChecked())
        if hasattr(self, 'hide_imprint_labels_check'):
            settings.setValue('hide_imprint_labels', self.hide_imprint_labels_check.isChecked())
        if hasattr(self, 'background_rendering_check'):
            settings.setValue('background_rendering', self.background_rendering_check.isChecked())
        if hasattr(self, 'imprint_cache_size_spin'):
            settings.setValue('imprint_cache_size', self.imprint_cache_size_spin.value())
        if hasattr(self, 'batch_size_spin'):
            settings.setValue('batch_size', self.batch_size_spin.value())

        # Sync settings to disk
        settings.sync()

        # Show confirmation message
        QtWidgets.QMessageBox.information(self, "Settings Saved", "All settings have been saved successfully.")

    def on_ok_clicked(self):
        """Handle OK button click with fetch data prompt"""
        # Save settings first
        self.save_settings()

        # Show fetch data prompt
        reply = QtWidgets.QMessageBox.question(
            self,
            "Fetch Data",
            "Settings have been applied. Would you like to fetch data to reload the chart with the new settings?",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            QtWidgets.QMessageBox.StandardButton.Yes
        )

        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            # Accept the dialog first
            self.accept()

            # Update candlestick chart visibility based on new settings
            if hasattr(self.parent(), 'update_candlestick_chart_visibility'):
                self.parent().update_candlestick_chart_visibility()

            # Trigger fetch data in the parent window
            if hasattr(self.parent(), 'fetch_data'):
                self.parent().fetch_data()
            elif hasattr(self.parent(), 'on_fetch_data'):
                self.parent().on_fetch_data()
        else:
            # Update candlestick chart visibility even if not fetching data
            if hasattr(self.parent(), 'update_candlestick_chart_visibility'):
                self.parent().update_candlestick_chart_visibility()

            # Just accept the dialog without fetching data
            self.accept()



class VectorRebaseChart(QtWidgets.QMainWindow):
    # Signal to notify other components when data is fetched
    data_fetched = QtCore.pyqtSignal(str, str, int)
    def __init__(self):
        super().__init__()
        # Chart colors - using dark grey theme
        self.chart_colors = {
            'background': '#1e1e1e',  # Dark grey background
            'bullish': '#4CAF50',  # Material Design Green
            'bearish': '#F44336',  # Material Design Red
            'vector': '#9C27B0',   # Material Design Purple (The Line)
            'pivot': '#FFC107',    # Material Design Amber
            'text': '#E0E0E0',     # Light gray text
            'support': '#00BCD4',  # Material Design Cyan for support levels
            'resistance': '#FF9800', # Material Design Orange for resistance levels
            'primary_accent': '#007acc'  # Blue for loading screen
        }

        # Create loading screen
        self.loading_screen = LoadingScreen(self, "Loading data...", self.chart_colors)
        self.settings_dialog = SettingsDialog(self)
        self.load_chart_settings()
        self.setGeometry(100, 100, 1200, 800)
        # Set up custom status bar with Data Source and fetch status
        self.setup_status_bar()

        # Connect to data dispatcher for status updates
        try:
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()
            dispatcher.progress.connect(self.on_data_progress)
        except ImportError:
            pass


        self.last_pivot_cross = None
        self.last_pivot_price = None
        self.pivot_crossed = False
        self.crossing_points = []
        self.visualization_pivot = None  # Initialize visualization_pivot attribute
        self.crossing_pivots = {}  # Dictionary to store pivot prices for each crossing point
        self.extrema_prices = {}  # Dictionary to store extrema prices (line price right before direction change)
        self.potential_crossings = {}
        self.confirmation_period = 3
        self.min_crossing_magnitude = 0.05

        # Initialize cycle_extreme_labels to prevent warnings
        self.cycle_extreme_labels = []

        # Initialize the SignalProcessor
        self.signal_processor = SignalProcessor(settings=self.settings_dialog, parent=self)
        logger.info("SignalProcessor initialized in VectorRebaseChart")



        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        layout = QtWidgets.QVBoxLayout(central_widget)
        self.plot_widget = pg.PlotWidget()
        # Set background color to dark grey
        self.plot_widget.setBackground('#1e1e1e')  # Dark grey background
        self.plot_widget.showGrid(x=True, y=True, alpha=0.1)
        self.plot_widget.getAxis('left').setTextPen(self.chart_colors['text'])
        self.plot_widget.getAxis('bottom').setTextPen(self.chart_colors['text'])

        # Create crosshair lines - white and dashed as per requirements
        # Import the crosshair utility to ensure consistent crosshairs across the application
        try:
            from crosshair_utility import add_crosshair

            # Define a function to get the current extrema value
            def get_current_extrema():
                if hasattr(self, 'current_pivot') and self.current_pivot is not None:
                    return self.current_pivot
                return None

            # Add crosshair to the plot widget with the extrema function
            crosshair_components = add_crosshair(
                self.plot_widget,
                hide_cursor=True,
                get_extrema_func=get_current_extrema
            )
            self.crosshair_v = crosshair_components['crosshair_v']
            self.crosshair_h = crosshair_components['crosshair_h']
            self.crosshair_text = crosshair_components['crosshair_text']
            self.proxy = crosshair_components['proxy']
        except ImportError:
            # Fallback to direct implementation if utility module is not available
            # White, dashed, width 2 pen for crosshair as per requirements
            white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
            self.crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
            self.crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
            self.plot_widget.addItem(self.crosshair_v, ignoreBounds=True)
            self.plot_widget.addItem(self.crosshair_h, ignoreBounds=True)

            # Add text item for displaying coordinates with improved visibility
            self.crosshair_text = pg.TextItem(
                text="",
                color='#FFFFFF',  # White text for better visibility
                fill=pg.mkBrush(self.chart_colors['background'] + 'CC'),  # More opaque background (80%)
                anchor=(0, 0),
                border=pg.mkPen(color='#FFFFFF55', width=1)  # Light border for better visibility
            )
            # Set a slightly larger font for better readability
            font = QtGui.QFont("Arial", 9)
            self.crosshair_text.setFont(font)
            self.plot_widget.addItem(self.crosshair_text)

            # Set up signal proxy for mouse movement
            self.proxy = pg.SignalProxy(
                self.plot_widget.scene().sigMouseMoved,
                rateLimit=60,
                slot=self.update_crosshair
            )

        # Initially hide the crosshairs
        self.crosshair_v.setVisible(False)
        self.crosshair_h.setVisible(False)
        self.crosshair_text.setVisible(False)


        controls_layout = QtWidgets.QHBoxLayout()

        # Create labels but don't display them (keep for functionality)
        self.vector_price_label = QtWidgets.QLabel("The Line: --")
        self.vector_price_label.setVisible(False)

        self.pivot_price_label = QtWidgets.QLabel("Extrema: --")
        self.pivot_price_label.setVisible(False)

        self.crossing_prediction_label = QtWidgets.QLabel("Crossing: --")
        self.crossing_prediction_label.setVisible(False)

        # Enhanced Signal Display - keep but don't display
        self.signal_display = EnhancedSignalDisplay()
        self.signal_display.setVisible(False)

        self.time_price_label = QtWidgets.QLabel("Time: -- | Price: --")
        self.time_price_label.setVisible(False)

        controls_layout.addStretch()
        # Import theme colors if available
        try:
            import theme
            THEME_COLORS = theme.DEFAULT
        except ImportError:
            # Fallback theme colors
            THEME_COLORS = {
                'background': '#1e1e1e',           # Dark gray background
                'control_panel': '#2d2d2d',        # Lighter gray control panels
                'borders': '#3e3e3e',              # Border color
                'text': '#e0e0e0',                 # Light gray text
                'primary_accent': '#007acc',       # Primary blue accent
                'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
                'pressed_accent': '#005c99',       # Pressed state blue (darker)
                'highlight': '#FFC107',            # Material Design Amber
                'selection': '#2979FF',            # Selection highlight color
                'button_radius': '4px',            # Button corner radius
                'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
            }

        # Symbol input (hidden - now controlled by universal controls)
        self.symbol_input = QtWidgets.QLineEdit()
        self.symbol_input.setPlaceholderText("Enter symbol (e.g. AAPL)")
        self.symbol_input.setFixedWidth(120)
        self.symbol_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
            }}
            QLineEdit:hover {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QLineEdit:focus {{
                outline: none;
                border: 2px solid {THEME_COLORS['selection']};
            }}
        """)
        self.symbol_input.setVisible(False)
        # Timeframe dropdown (hidden - now controlled by universal controls)
        self.timeframe_combo = QtWidgets.QComboBox()
        self.timeframe_combo.addItems(["1m", "2m", "5m", "15m", "30m", "60m", "1d"])
        self.timeframe_combo.setCurrentText(default_registry.get_value('timeframe'))
        self.timeframe_combo.setFixedWidth(80)
        self.timeframe_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
            }}
            QComboBox:hover {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QComboBox:focus {{
                outline: none;
                border: 2px solid {THEME_COLORS['selection']};
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                selection-background-color: {THEME_COLORS['selection']};
                selection-color: white;
            }}
        """)
        self.timeframe_combo.setVisible(False)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        # Days to Load input (hidden - now controlled by universal controls)
        self.dtl_spin = QtWidgets.QSpinBox()
        self.dtl_spin.setRange(1, 20000)  # Increased to allow 15k+ days
        self.dtl_spin.setValue(default_registry.get_value('days_to_load'))
        self.dtl_spin.setFixedWidth(80)
        self.dtl_spin.setSuffix(" days")
        self.dtl_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
            }}
            QSpinBox:hover {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QSpinBox:focus {{
                outline: none;
                border: 2px solid {THEME_COLORS['selection']};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS['control_panel']};
                border: none;
                width: 16px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS['secondary_accent']};
            }}
            QSpinBox::up-button:pressed, QSpinBox::down-button:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)
        self.dtl_spin.setVisible(False)
        self.dtl_spin.valueChanged.connect(self.on_days_to_load_changed)

        # Visual controls moved to settings dialog
        self.show_vector = QtWidgets.QCheckBox()
        self.show_vector.setChecked(True)
        self.show_vector.setVisible(False)

        self.show_peak_trough_rays = QtWidgets.QCheckBox()
        self.show_peak_trough_rays.setChecked(True)
        self.show_peak_trough_rays.setVisible(False)

        self.show_probability_bands = QtWidgets.QCheckBox()
        self.show_probability_bands.setChecked(True)
        self.show_probability_bands.setVisible(False)
        self.show_probability_bands.stateChanged.connect(lambda: self.plot_data_until(self.current_idx))

        self.show_clusters = QtWidgets.QCheckBox()
        self.show_clusters.setChecked(True)
        self.show_clusters.setVisible(False)
        self.show_clusters.stateChanged.connect(lambda: self.plot_data_until(self.current_idx))

        self.show_imprints = QtWidgets.QCheckBox()
        self.show_imprints.setChecked(True)
        self.show_imprints.setVisible(False)
        self.show_imprints.stateChanged.connect(self.toggle_imprints_visibility)

        # Auto-fetch moved to settings dialog
        self.auto_fetch = QtWidgets.QCheckBox()
        self.auto_fetch.setChecked(False)
        self.auto_fetch.setVisible(False)

        # Add countdown label (hidden but kept for functionality)
        self.countdown_label = QtWidgets.QLabel("60s")
        self.countdown_label.setStyleSheet("color: #FFC107; font-weight: bold;") # Amber color
        self.countdown_label.setVisible(False)

        # Connect auto-fetch toggle to start/stop timer
        self.auto_fetch.stateChanged.connect(self.toggle_auto_fetch)

        # Create countdown timer
        self.countdown_timer = QtCore.QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_timer.setInterval(1000)  # Update every second
        self.countdown_seconds = 60





        # Add vector length spinner with Midnight Ocean theme
        vector_layout = QtWidgets.QHBoxLayout()
        vector_label = QtWidgets.QLabel("The Line Length:")
        vector_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        vector_label.setVisible(False)
        vector_layout.addWidget(vector_label)

        # Vector Length input (hidden - now controlled by universal controls)
        self.vector_length_spin = QtWidgets.QSpinBox()
        self.vector_length_spin.setRange(1, 1000)
        self.vector_length_spin.setValue(default_registry.get_value('vector_length'))
        self.vector_length_spin.setFixedWidth(80)
        self.vector_length_spin.setToolTip("Lookback period for Donchian channel calculation")
        self.vector_length_spin.setVisible(False)
        self.vector_length_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
            }}
            QSpinBox:hover {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QSpinBox:focus {{
                outline: none;
                border: 2px solid {THEME_COLORS['selection']};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS['control_panel']};
                border: none;
                width: 16px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS['secondary_accent']};
            }}
            QSpinBox::up-button:pressed, QSpinBox::down-button:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)
        self.vector_length_spin.valueChanged.connect(self.vector_length_changed)
        vector_layout.addWidget(self.vector_length_spin)

        # Apply stored vector length value if it exists
        if hasattr(self, '_vector_length_value'):
            self.vector_length_spin.setValue(self._vector_length_value)
            delattr(self, '_vector_length_value')  # Clean up temporary attribute

        controls_layout.addLayout(vector_layout)

        # Fetch Data button (hidden - now controlled by universal controls)
        self.fetch_button = QtWidgets.QPushButton("Fetch Data")
        self.fetch_button.clicked.connect(self.fetch_data)
        self.fetch_button.setFixedWidth(120)
        self.fetch_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #000000;
                color: {THEME_COLORS['text']};
                border: none;
                padding: 8px 15px;
                border-radius: {THEME_COLORS['button_radius']};
                font-weight: 500;
                border: 1px solid #333333;
                margin: 2px 2px 5px 2px; /* top right bottom left */
                box-shadow: {THEME_COLORS['button_shadow']};
            }}
            QPushButton:hover {{
                background-color: #333333;
                border: 1px solid #555555;
                margin: 0px 0px 7px 4px; /* Shift button to simulate pressed state */
            }}
            QPushButton:pressed {{
                background-color: #111111;
                border: 1px solid #444444;
                margin: 5px 0px 2px 4px; /* Shift button to simulate pressed state */
            }}
            QPushButton:focus {{
                outline: none;
                border: 2px solid #a0a0a0;
            }}
        """)
        self.fetch_button.setVisible(False)



        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        charts_layout = QtWidgets.QHBoxLayout()
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.showGrid(x=False, y=False)
        # Set background color to dark grey
        self.plot_widget.setBackground('#1e1e1e')  # Dark grey background
        self.imprints_widget = pg.PlotWidget()
        # Set background color to dark grey
        self.imprints_widget.setBackground('#1e1e1e')  # Dark grey background
        self.imprints_widget.showGrid(y=False, x=False)
        self.imprints_widget.setFixedWidth(100)
        self.imprints_widget.setYLink(self.plot_widget)
        # Hide all axes for the imprints widget (right chart)
        self.imprints_widget.showAxis('left', False)
        self.imprints_widget.showAxis('right', False)
        self.imprints_widget.showAxis('top', False)
        self.imprints_widget.showAxis('bottom', False)
        self.imprints_widget.setVisible(True)

        # Create a box widget to the right of the chart with 1/5th of the chart's width
        self.right_box = QtWidgets.QWidget()
        self.right_box.setFixedWidth(200)  # Approximately 1/5th of typical chart width
        self.right_box.setStyleSheet("background-color: #1e1e1e; border: 1px solid #3e3e3e;")

        # Create a splitter for the right box to make sections resizable
        self.right_box_splitter = QtWidgets.QSplitter(QtCore.Qt.Orientation.Vertical)
        self.right_box_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #555555;
                height: 3px;
            }
            QSplitter::handle:hover {
                background-color: #777777;
            }
        """)

        # Create a layout for the right box
        right_box_layout = QtWidgets.QVBoxLayout(self.right_box)
        right_box_layout.setContentsMargins(5, 5, 5, 5)
        right_box_layout.addWidget(self.right_box_splitter)

        # Create Information section with scroll area
        self.info_scroll = QtWidgets.QScrollArea()
        self.info_scroll.setWidgetResizable(True)
        self.info_scroll.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.info_scroll.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.info_scroll.setStyleSheet("""
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2e2e2e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """)

        self.info_section = QtWidgets.QWidget()
        self.info_section.setStyleSheet("background-color: #1e1e1e;")
        info_section_layout = QtWidgets.QVBoxLayout(self.info_section)
        info_section_layout.setContentsMargins(5, 5, 5, 5)
        info_section_layout.setSpacing(2)

        # Add "Information" title
        info_title = QtWidgets.QLabel("Information")
        info_title.setStyleSheet("color: white; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 14px; padding-bottom: 5px; border: none;")
        info_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        info_section_layout.addWidget(info_title)

        # Create labels for candle information
        self.idx_label = QtWidgets.QLabel("IDX: --")
        self.date_label = QtWidgets.QLabel("Date: --")
        self.open_label = QtWidgets.QLabel("Open: --")
        self.high_label = QtWidgets.QLabel("High: --")
        self.low_label = QtWidgets.QLabel("Low: --")
        self.close_label = QtWidgets.QLabel("Close: --")
        self.volume_label = QtWidgets.QLabel("Volume: --")
        self.current_pivot_label = QtWidgets.QLabel("Current Extrema: --")
        self.candle_pivot_label = QtWidgets.QLabel("Prev Cycle Extrema: --")
        self.line_price_label = QtWidgets.QLabel("Line Price: --")

        # Add new labels for average cycle number information
        self.avg_peak_idx_label = QtWidgets.QLabel("Avg Peak Cycle: --")
        self.avg_trough_idx_label = QtWidgets.QLabel("Avg Trough Cycle: --")

        # Add crosshair price label to show percentage from extrema and converted price
        self.crosshair_price_label = QtWidgets.QLabel("Crosshair: --")
        self.crosshair_price_label.setStyleSheet("color: #FFFFFF; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold;")

        # Add a separator line below the candle pivot
        self.separator_line = QtWidgets.QFrame()
        self.separator_line.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        self.separator_line.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        self.separator_line.setStyleSheet("background-color: #555555;")
        self.separator_line.setFixedHeight(2)

        # Set text color to white for all labels
        label_style = "color: white; font-family: 'Consolas', 'Courier New', monospace;"
        for label in [self.idx_label, self.date_label, self.open_label, self.high_label,
                     self.low_label, self.close_label, self.volume_label,
                     self.current_pivot_label, self.candle_pivot_label, self.line_price_label,
                     self.avg_peak_idx_label, self.avg_trough_idx_label]:
            label.setStyleSheet(label_style)
            info_section_layout.addWidget(label)

        # Add the crosshair price label
        info_section_layout.addWidget(self.crosshair_price_label)

        # Set the info section as the scroll area's widget
        self.info_scroll.setWidget(self.info_section)

        # Add the scroll area to the splitter
        self.right_box_splitter.addWidget(self.info_scroll)

        # Create Drawing Tools section with scroll area
        self.drawing_tools_scroll = QtWidgets.QScrollArea()
        self.drawing_tools_scroll.setWidgetResizable(True)
        self.drawing_tools_scroll.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.drawing_tools_scroll.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.drawing_tools_scroll.setStyleSheet("""
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2e2e2e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """)

        self.drawing_tools_section = QtWidgets.QWidget()
        self.drawing_tools_section.setStyleSheet("background-color: #1e1e1e;")
        drawing_tools_layout = QtWidgets.QVBoxLayout(self.drawing_tools_section)
        drawing_tools_layout.setContentsMargins(5, 5, 5, 5)
        drawing_tools_layout.setSpacing(2)

        # Add "Drawing Tools" title
        drawing_tools_title = QtWidgets.QLabel("Drawing Tools")
        drawing_tools_title.setStyleSheet("color: white; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 14px; padding-top: 5px; padding-bottom: 5px; border: none;")
        drawing_tools_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        drawing_tools_layout.addWidget(drawing_tools_title)

        # Create drawing tool buttons
        button_style = """
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }
        """

        # Mouse button
        self.mouse_button = QtWidgets.QPushButton("Mouse")
        self.mouse_button.setStyleSheet(button_style)
        self.mouse_button.clicked.connect(lambda: self.set_drawing_tool(None))
        drawing_tools_layout.addWidget(self.mouse_button)

        # Redo button
        self.redo_button = QtWidgets.QPushButton("Redo")
        self.redo_button.setStyleSheet(button_style)
        self.redo_button.clicked.connect(self.redo_drawing)
        drawing_tools_layout.addWidget(self.redo_button)

        # Undo button
        self.undo_button = QtWidgets.QPushButton("Undo")
        self.undo_button.setStyleSheet(button_style)
        self.undo_button.clicked.connect(self.undo_drawing)
        drawing_tools_layout.addWidget(self.undo_button)

        # Line button
        self.line_button = QtWidgets.QPushButton("Line")
        self.line_button.setStyleSheet(button_style)
        self.line_button.clicked.connect(lambda: self.set_drawing_tool('line'))
        drawing_tools_layout.addWidget(self.line_button)

        # Vertical Line button
        self.vertical_line_button = QtWidgets.QPushButton("Vertical Line")
        self.vertical_line_button.setStyleSheet(button_style)
        self.vertical_line_button.clicked.connect(lambda: self.set_drawing_tool('vertical'))
        drawing_tools_layout.addWidget(self.vertical_line_button)

        # Horizontal Line button
        self.horizontal_line_button = QtWidgets.QPushButton("Horizontal Line")
        self.horizontal_line_button.setStyleSheet(button_style)
        self.horizontal_line_button.clicked.connect(lambda: self.set_drawing_tool('horizontal'))
        drawing_tools_layout.addWidget(self.horizontal_line_button)

        # Rectangle button
        self.rectangle_button = QtWidgets.QPushButton("Rectangle")
        self.rectangle_button.setStyleSheet(button_style)
        self.rectangle_button.clicked.connect(lambda: self.set_drawing_tool('rectangle'))
        drawing_tools_layout.addWidget(self.rectangle_button)

        # Text button
        self.text_button = QtWidgets.QPushButton("Text")
        self.text_button.setStyleSheet(button_style)
        self.text_button.clicked.connect(lambda: self.set_drawing_tool('text'))
        drawing_tools_layout.addWidget(self.text_button)

        # Anchored Text button
        self.anchored_text_button = QtWidgets.QPushButton("Anchored Text")
        self.anchored_text_button.setStyleSheet(button_style)
        self.anchored_text_button.clicked.connect(self.add_anchored_text)
        drawing_tools_layout.addWidget(self.anchored_text_button)

        # Clear All button
        self.clear_all_button = QtWidgets.QPushButton("Clear All")
        self.clear_all_button.setStyleSheet(button_style)
        self.clear_all_button.clicked.connect(self.clear_all_drawings)
        drawing_tools_layout.addWidget(self.clear_all_button)

        # Set the drawing tools section as the scroll area's widget
        self.drawing_tools_scroll.setWidget(self.drawing_tools_section)

        # Add the drawing tools scroll area to the splitter
        self.right_box_splitter.addWidget(self.drawing_tools_scroll)

        # Create Settings section with scroll area
        self.settings_scroll = QtWidgets.QScrollArea()
        self.settings_scroll.setWidgetResizable(True)
        self.settings_scroll.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.settings_scroll.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.settings_scroll.setStyleSheet("""
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2e2e2e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """)

        self.settings_section = QtWidgets.QWidget()
        self.settings_section.setStyleSheet("background-color: #1e1e1e;")
        settings_layout = QtWidgets.QVBoxLayout(self.settings_section)
        settings_layout.setContentsMargins(5, 5, 5, 5)
        settings_layout.setSpacing(2)

        # Add "Settings" title
        settings_title = QtWidgets.QLabel("Settings")
        settings_title.setStyleSheet("color: white; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 14px; padding-top: 5px; padding-bottom: 5px; border: none;")
        settings_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(settings_title)

        # Add "Chart Settings" subtitle
        chart_settings_subtitle = QtWidgets.QLabel("Chart Settings")
        chart_settings_subtitle.setStyleSheet("color: white; font-family: 'Consolas', 'Courier New', monospace; font-style: italic; font-size: 12px; padding-bottom: 5px; border: none;")
        chart_settings_subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(chart_settings_subtitle)



        # Create settings button
        self.settings_button_panel = QtWidgets.QPushButton("Settings")
        self.settings_button_panel.setStyleSheet("""
            QPushButton {
                background-color: #000000;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #333333;
                border: 1px solid #555555;
            }
            QPushButton:pressed {
                background-color: #111111;
                border: 1px solid #444444;
            }
        """)
        self.settings_button_panel.clicked.connect(self.open_settings_dialog)
        settings_layout.addWidget(self.settings_button_panel)

        # Add "Data Connections" subtitle
        data_connections_subtitle = QtWidgets.QLabel("Data Connections")
        data_connections_subtitle.setStyleSheet("color: white; font-family: 'Consolas', 'Courier New', monospace; font-style: italic; font-size: 12px; padding-top: 10px; padding-bottom: 5px; border: none;")
        data_connections_subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(data_connections_subtitle)

        # Create Schwab Login button
        self.schwab_login_button = QtWidgets.QPushButton("Schwab Login")
        self.schwab_login_button.setStyleSheet("""
            QPushButton {
                background-color: #000000;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #333333;
                border: 1px solid #555555;
            }
            QPushButton:pressed {
                background-color: #111111;
                border: 1px solid #444444;
            }
        """)
        self.schwab_login_button.clicked.connect(self.show_schwab_login)
        settings_layout.addWidget(self.schwab_login_button)

        # Add stretch at the bottom to push all labels and buttons to the top
        settings_layout.addStretch()

        # Set the settings section as the scroll area's widget
        self.settings_scroll.setWidget(self.settings_section)

        # Add the settings scroll area to the splitter
        self.right_box_splitter.addWidget(self.settings_scroll)

        # Set initial splitter sizes (give more space to information section)
        self.right_box_splitter.setSizes([300, 200, 150])  # Info, Drawing Tools, Settings

        # Set minimum sizes for each section to prevent them from disappearing
        self.right_box_splitter.setChildrenCollapsible(False)

        # Add a third separator line below the data connections section
        self.settings_separator_line = QtWidgets.QFrame()
        self.settings_separator_line.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        self.settings_separator_line.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        self.settings_separator_line.setStyleSheet("background-color: #555555;")
        self.settings_separator_line.setFixedHeight(2)
        right_box_layout.addWidget(self.settings_separator_line)

        # Data Source moved to bottom margin area below charts

        # Add stretch at the bottom to push all labels and buttons to the top
        right_box_layout.addStretch()

        charts_layout.addWidget(self.plot_widget, 1)
        charts_layout.addWidget(self.imprints_widget, 0)
        charts_layout.addWidget(self.right_box, 0)  # Add with stretch factor 0 to maintain fixed width

        # Create a vertical splitter to hold the main chart area and the candlestick chart
        main_splitter = QtWidgets.QSplitter(QtCore.Qt.Orientation.Vertical)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: transparent;
                height: 5px;
                border: none;
            }
            QSplitter::handle:hover {
                background-color: transparent;
            }
        """)

        # Create a widget to hold the current chart layout
        main_chart_widget = QtWidgets.QWidget()
        main_chart_layout = QtWidgets.QHBoxLayout(main_chart_widget)
        main_chart_layout.setContentsMargins(0, 0, 0, 0)
        main_chart_layout.addLayout(charts_layout)

        # Vector 50 chart completely removed - candlestick_chart_widget no longer created

        # Create the Bollinger width chart widget below the vector 50 chart
        self.bollinger_width_chart_widget = pg.PlotWidget()
        self.bollinger_width_chart_widget.setBackground('#1e1e1e')  # Dark grey background
        self.bollinger_width_chart_widget.showGrid(x=False, y=False, alpha=0.3)  # Hide grid as well
        # Hide all axes for a clean appearance
        self.bollinger_width_chart_widget.showAxis('left', False)
        self.bollinger_width_chart_widget.showAxis('right', False)
        self.bollinger_width_chart_widget.showAxis('top', False)
        self.bollinger_width_chart_widget.showAxis('bottom', False)

        # Link the X-axis of the Bollinger width chart to the main chart
        self.bollinger_width_chart_widget.setXLink(self.plot_widget)

        # Initialize candlestick chart items
        self.bottom_candlestick_item = None
        self.bottom_vector_item = None
        self.bollinger_width_item = None

        # Vector 50 chart completely removed - no visibility setting needed for candlestick_chart_widget

        # Create a widget to hold the bottom charts with the same width as the chart area
        bottom_charts_widget = QtWidgets.QWidget()
        bottom_charts_main_layout = QtWidgets.QVBoxLayout(bottom_charts_widget)
        bottom_charts_main_layout.setContentsMargins(0, 0, 0, 0)
        bottom_charts_main_layout.setSpacing(0)

        # Vector 50 chart completely removed - no longer takes up any space
        # vector_chart_widget = QtWidgets.QWidget()
        # vector_chart_layout = QtWidgets.QHBoxLayout(vector_chart_widget)
        # vector_chart_layout.setContentsMargins(0, 0, 0, 0)
        # vector_chart_layout.addWidget(self.candlestick_chart_widget, 1)  # Main chart width
        # imprints_spacer = QtWidgets.QWidget()
        # imprints_spacer.setFixedWidth(100)  # Same width as imprints_widget
        # vector_chart_layout.addWidget(imprints_spacer, 0)
        # right_spacer = QtWidgets.QWidget()
        # right_spacer.setFixedWidth(200)  # Same width as right_box
        # vector_chart_layout.addWidget(right_spacer, 0)

        # Create the Bollinger width chart container
        bollinger_chart_widget = QtWidgets.QWidget()
        bollinger_chart_layout = QtWidgets.QHBoxLayout(bollinger_chart_widget)
        bollinger_chart_layout.setContentsMargins(0, 0, 0, 0)

        # Add the Bollinger width chart spanning the width of main chart + imprints widget
        bollinger_chart_layout.addWidget(self.bollinger_width_chart_widget, 1)  # Main chart width

        # Add a spacer widget with the same width as the imprints widget
        imprints_spacer_2 = QtWidgets.QWidget()
        imprints_spacer_2.setFixedWidth(100)  # Same width as imprints_widget
        bollinger_chart_layout.addWidget(imprints_spacer_2, 0)

        # Add a spacer widget with the same width as the right box
        right_spacer_2 = QtWidgets.QWidget()
        right_spacer_2.setFixedWidth(200)  # Same width as right_box
        bollinger_chart_layout.addWidget(right_spacer_2, 0)

        # Add only the Bollinger width chart container to the bottom charts widget
        # Vector 50 chart completely removed - no longer takes up any space
        # bottom_charts_main_layout.addWidget(vector_chart_widget, 1)  # Vector 50 chart (removed)
        bottom_charts_main_layout.addWidget(bollinger_chart_widget, 1)  # Bollinger width chart (now takes all space)

        # Add both widgets to the splitter
        main_splitter.addWidget(main_chart_widget)
        main_splitter.addWidget(bottom_charts_widget)

        # Set minimum sizes to ensure both sections are resizable
        main_chart_widget.setMinimumHeight(200)  # Minimum height for main chart
        bottom_charts_widget.setMinimumHeight(50)  # Minimum height for Bollinger chart

        # Set initial sizes (main chart gets most space, bottom chart gets smaller portion)
        main_splitter.setSizes([500, 100])  # Increased bottom chart initial size for better visibility
        # Allow children to be collapsible so bottom chart can be fully collapsed
        main_splitter.setChildrenCollapsible(True)

        # Set stretch factors to allow proper resizing
        main_splitter.setStretchFactor(0, 1)  # Main chart can stretch
        main_splitter.setStretchFactor(1, 0)  # Bottom chart maintains size preference

        # Add the splitter to the main layout instead of the charts_layout
        layout.addWidget(main_splitter)

        # Update candlestick chart visibility after settings are loaded
        self.update_candlestick_chart_visibility()
        self.plot_widget.getViewBox().sigRangeChanged.connect(self.sync_imprints_view)
        # Vector 50 chart completely removed - no bottom chart sync needed
        self.plot_widget.getViewBox().sigRangeChanged.connect(self.update_threshold_labels)
        self.plot_widget.getViewBox().sigRangeChanged.connect(self.enforce_view_limits)

        # Connect to resizeEvent to ensure right_box maintains same height as plot_widget
        self.plot_widget.installEventFilter(self)
        # Create white, dashed, width 2 crosshair lines as per requirements
        white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
        self.vLine = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
        self.hLine = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
        self.plot_widget.addItem(self.vLine, ignoreBounds=True)
        self.plot_widget.addItem(self.hLine, ignoreBounds=True)
        self.proxy = pg.SignalProxy(self.plot_widget.scene().sigMouseMoved,
                                  rateLimit=60, slot=self.mouseMoved)
        self.data = None
        self.candlestick_item = None
        self.vector_line = None
        self.pivot_line = None
        self.vector_price_box = None
        self.pivot_label = None
        self.current_pivot = None
        self.last_vector_price = None
        self.below_vector = False
        self.current_idx = 0
        self.update_timer = QtCore.QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.setInterval(60000)  # 60 seconds
        self.iv_update_timer = QtCore.QTimer()
        self.iv_update_timer.timeout.connect(self.update_iv)
        self.iv_update_timer.setInterval(300000)
        self.crossing_classifier = DummyClassifier()
        self.peak_trough_rays = PeakTroughRays(self.plot_widget)

        # Drawing toolbar removed to eliminate strip under tab bar
        # self.drawing_toolbar = QtWidgets.QToolBar()
        # self.drawing_toolbar.setMovable(False)
        # self.drawing_toolbar.setFloatable(False)

        # Connect to parameter registry signals
        default_registry.ui_parameter_changed.connect(self.on_parameter_registry_changed)
        # self.addToolBar(QtCore.Qt.ToolBarArea.TopToolBarArea, self.drawing_toolbar)

        self.line_tool = QtGui.QAction("Line", self)
        self.line_tool.setCheckable(True)
        self.rectangle_tool = QtGui.QAction("Rectangle", self)
        self.rectangle_tool.setCheckable(True)
        self.horizontal_tool = QtGui.QAction("Horizontal", self)
        self.horizontal_tool.setCheckable(True)
        self.vertical_tool = QtGui.QAction("Vertical", self)
        self.vertical_tool.setCheckable(True)
        self.text_tool = QtGui.QAction("Text", self)
        self.text_tool.setCheckable(True)
        self.clear_drawings = QtGui.QAction("Clear All", self)





        # Settings button moved to the right panel
        self.line_tool.triggered.connect(lambda: self.set_drawing_tool('line'))
        self.rectangle_tool.triggered.connect(lambda: self.set_drawing_tool('rectangle'))
        self.horizontal_tool.triggered.connect(lambda: self.set_drawing_tool('horizontal'))
        self.vertical_tool.triggered.connect(lambda: self.set_drawing_tool('vertical'))
        self.text_tool.triggered.connect(lambda: self.set_drawing_tool('text'))
        self.clear_drawings.triggered.connect(self.clear_all_drawings)
        self.drawing_tools = DrawingTools(self.plot_widget)
        self.current_drawing_tool = None
        self.plot_widget.scene().sigMouseClicked.connect(self.mouse_clicked)
        self.plot_widget.scene().sigMouseMoved.connect(self.mouse_moved)
        self.plot_widget.scene().sigMouseClicked.connect(self.handle_right_click)
        self.setStyleSheet("""
            QMainWindow { background-color: #2b2b2b; }
            QLabel { color: white; }
            QCheckBox { color: white; }
            QPushButton { background-color: #4a4a4a; color: white; border: none; padding: 5px; }
            QPushButton:hover { background-color: #5a5a5a; }
            QComboBox {
                background-color: #4a4a4a;
                color: white;
                border: none;
                padding: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #2b2b2b;
                color: white;
                selection-background-color: #4a4a4a;
                selection-color: white;
                border: none;
            }
            QComboBox::drop-down {
                border: none;
                background: #4a4a4a;
            }
            QComboBox::down-arrow {
                border: none;
                color: white;
            }
            QLineEdit { background-color: #4a4a4a; color: white; border: none; padding: 5px; }
            QSpinBox { background-color: #4a4a4a; color: white; border: none; padding: 5px; }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: #4a4a4a;
                margin: 2px 0;
            }
            QSlider::handle:horizontal {
                background: #999999;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 3px;
            }
        """)
        self.settings_dialog = SettingsDialog(self)
        self.threshold_lines = []
        self.threshold_labels = []

        # Now load settings after UI is created
        self.load_chart_settings()

        self.peak_trough_rays = PeakTroughRays(self.plot_widget)

        # Initialize white lines for average cycle only
        self.white_lines = {
            'average_cycle': None
        }

    def update_candlestick_chart_visibility(self):
        """Update the visibility of the Bollinger width chart based on High Trend setting"""
        # Vector 50 chart completely removed - no longer exists
        # if not hasattr(self, 'candlestick_chart_widget'):
        #     return

        # Check if high trend is enabled in settings (default to True)
        high_trend_enabled = True  # Default to enabled
        if hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'high_trend_check'):
            high_trend_enabled = self.settings_dialog.high_trend_check.isChecked()

        # Vector 50 chart completely removed - no longer exists
        # self.candlestick_chart_widget.setVisible(high_trend_enabled)

        # Update Bollinger width chart visibility
        if hasattr(self, 'bollinger_width_chart_widget'):
            self.bollinger_width_chart_widget.setVisible(high_trend_enabled)

    def update_candlestick_chart(self):
        """Update the bottom Bollinger width chart with current data"""
        if self.data is None:
            return

        # Check if main chart is properly initialized
        if not hasattr(self, 'rebased_data') or not hasattr(self, 'rebased_vector') or len(self.rebased_data) == 0:
            return

        # Check if we have a valid current_idx
        if not hasattr(self, 'current_idx') or self.current_idx is None:
            return

        # Check if high trend is enabled in settings (default to True)
        high_trend_enabled = True  # Default to enabled
        if hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'high_trend_check'):
            high_trend_enabled = self.settings_dialog.high_trend_check.isChecked()

        # If high trend is disabled, hide the Bollinger width chart and return
        if not high_trend_enabled:
            # Vector 50 chart completely removed - no longer exists
            # self.candlestick_chart_widget.setVisible(False)
            if hasattr(self, 'bollinger_width_chart_widget'):
                self.bollinger_width_chart_widget.setVisible(False)
            return

        # If high trend is enabled, make sure the Bollinger width chart is visible
        # Vector 50 chart completely removed - no longer exists
        # self.candlestick_chart_widget.setVisible(True)
        if hasattr(self, 'bollinger_width_chart_widget'):
            self.bollinger_width_chart_widget.setVisible(True)

        try:
            # Vector 50 chart completely removed - no longer exists
            # Clear existing items
            # if self.bottom_candlestick_item is not None:
            #     self.candlestick_chart_widget.removeItem(self.bottom_candlestick_item)
            # if self.bottom_vector_item is not None:
            #     self.candlestick_chart_widget.removeItem(self.bottom_vector_item)

            # Vector 50 chart data preparation completely removed
            # candlestick_data = []
            # for i, (timestamp, row) in enumerate(self.data.iterrows()):
            #     candlestick_data.append((i, row['Open'], row['High'], row['Low'], row['Close']))
            # self.bottom_candlestick_item = None
            # vector_50 = self.calculate_vector(self.data, 50)
            # All vector 50 chart creation code removed

            # Update Bollinger width chart
            if hasattr(self, 'bollinger_width_chart_widget'):
                # Clear existing Bollinger width item
                if self.bollinger_width_item is not None:
                    self.bollinger_width_chart_widget.removeItem(self.bollinger_width_item)

                # Get "The Line Length" from parameter registry
                from parameter_registry import default_registry
                line_length = default_registry.get_value('vector_length')

                # Calculate skip_candles to match main chart visibility
                skip_candles = line_length + 1

                # Calculate Bollinger width using the close prices and "The Line Length"
                bollinger_width = bbw_from_close_only(self.data, length=line_length)

                # Create x-coordinates for the Bollinger width, starting from skip_candles to match main chart
                x_coords = list(range(skip_candles, len(bollinger_width)))
                y_coords = bollinger_width.iloc[skip_candles:].fillna(0).tolist()  # Skip first skip_candles values

                # Create colored line based on candle categories (H/L)
                if hasattr(self, 'current_pivot') and self.current_pivot is not None:
                    # Clear any existing bollinger width items first
                    if hasattr(self, 'bollinger_width_items') and self.bollinger_width_items:
                        for item in self.bollinger_width_items:
                            self.bollinger_width_chart_widget.removeItem(item)

                    # Initialize list to store multiple plot items for different colored segments
                    self.bollinger_width_items = []

                    # Get the current vector length from parameter registry
                    from parameter_registry import default_registry
                    vector_length = default_registry.get_value('vector_length')

                    # Skip drawing the Bollinger band chart data when vector length is 1
                    # but still allow the horizontal line at 3.0 to be drawn later
                    if vector_length != 1:
                        # Create segments with different colors based on current vector position
                        current_segment_color = None
                        segment_x = []
                        segment_y = []

                        # Calculate the current vector (same as main chart)
                        current_vector = self.calculate_vector(self.data, vector_length)

                        for i, (x, y) in enumerate(zip(x_coords, y_coords)):
                            # Calculate the actual data index (x is the chart x-coordinate, which starts from skip_candles)
                            data_index = x
                            if data_index < len(self.data):
                                # Default to red
                                color = self.chart_colors['bearish']

                                # Use the current vector data (same as main chart) for coloring
                                if len(current_vector) > 0 and data_index < len(current_vector):
                                    close_price = self.data['Close'].iloc[data_index]
                                    vector_price = current_vector.iloc[data_index]

                                    # Color based on price position relative to current vector
                                    if close_price > vector_price:
                                        # Price above current vector - green
                                        color = self.chart_colors['bullish']
                                    else:
                                        # Price below current vector - red
                                        color = self.chart_colors['bearish']
                                else:
                                    # Fallback to old logic if no vector available
                                    current_price = self.data['Close'].iloc[data_index]
                                    if current_price >= self.current_pivot:
                                        color = self.chart_colors['bullish']  # Green for price above extrema/0%
                                    else:
                                        color = self.chart_colors['bearish']  # Red for price below extrema/0%

                                # If color changed, finish current segment and start new one
                                if current_segment_color != color:
                                    # Draw previous segment if it exists and has more than one point
                                    if len(segment_x) > 1 and current_segment_color is not None:
                                        segment_item = self.bollinger_width_chart_widget.plot(
                                            segment_x, segment_y,
                                            pen=pg.mkPen(current_segment_color, width=2),
                                            name='Bollinger Width'
                                        )
                                        self.bollinger_width_items.append(segment_item)

                                    # Start new segment (include last point for continuity if exists)
                                    if segment_x and segment_y:
                                        segment_x = [segment_x[-1], x]  # Include last point for continuity
                                        segment_y = [segment_y[-1], y]
                                    else:
                                        segment_x = [x]
                                        segment_y = [y]
                                    current_segment_color = color
                                else:
                                    # Continue current segment
                                    segment_x.append(x)
                                    segment_y.append(y)

                        # Draw the final segment if it has more than one point
                        if len(segment_x) > 1 and current_segment_color is not None:
                            final_item = self.bollinger_width_chart_widget.plot(
                                segment_x, segment_y,
                                pen=pg.mkPen(current_segment_color, width=2),
                                name='Bollinger Width'
                            )
                            self.bollinger_width_items.append(final_item)
                            self.bollinger_width_item = final_item  # Keep reference to last item
                else:
                    # Fallback to cyan if no pivot available (only if vector length is not 1)
                    if vector_length != 1:
                        self.bollinger_width_item = self.bollinger_width_chart_widget.plot(
                            x_coords, y_coords,
                            pen=pg.mkPen('cyan', width=2),
                            name='Bollinger Width'
                        )

                # Set appropriate view range for Bollinger width chart
                # Get vector length to check if we need to set a default range for length 1 charts
                from parameter_registry import default_registry
                vector_length = default_registry.get_value('vector_length')

                if len(y_coords) > 0 and vector_length != 1:
                    valid_values = [y for y in y_coords if not np.isnan(y) and y != 0]
                    if valid_values:
                        min_bbw = min(valid_values)
                        max_bbw = max(valid_values)

                        # Include the vector 50 line at max_bbw + 2.0 in the range calculation
                        vector_line_position = max_bbw + 2.0
                        min_range = min(min_bbw, vector_line_position)
                        max_range = max(max_bbw, vector_line_position)
                        bbw_range = max_range - min_range

                        # Add some padding
                        padding = bbw_range * 0.1 if bbw_range > 0 else 1
                        self.bollinger_width_chart_widget.setYRange(min_range - padding, max_range + padding)
                        self.bollinger_width_chart_widget.setXRange(0, len(x_coords))

                        # Add horizontal reference lines at 25%, 50%, 75% of peak
                        self.add_bollinger_reference_lines(max_bbw)

                        # Add horizontal line strip 5% above the highest peak with vector 50 chart colors
                        self.add_bollinger_indicator_line(x_coords, y_coords, max_bbw)
                elif vector_length == 1:
                    # For length 1 charts, use a default max_bbw of 3.0 for the vector line positioning
                    default_max_bbw = 3.0
                    vector_line_position = default_max_bbw + 2.0
                    # Set range to accommodate the vector line position
                    self.bollinger_width_chart_widget.setYRange(2.0, vector_line_position + 1.0)
                    if len(x_coords) > 0:
                        self.bollinger_width_chart_widget.setXRange(0, len(x_coords))

                    # Add horizontal reference lines at 25%, 50%, 75% of default peak
                    self.add_bollinger_reference_lines(default_max_bbw)

                    # Add the horizontal line +2 above the default position
                    self.add_bollinger_indicator_line(x_coords, y_coords, default_max_bbw)

        except Exception as e:
            print(f"Error updating candlestick chart: {e}")
            import traceback
            traceback.print_exc()

    def add_bollinger_indicator_line(self, x_coords, y_coords, max_bbw):
        """Add a horizontal line strip +2 above the bollinger width line peak with vector 50 chart colors"""
        try:
            # Clear any existing indicator line
            if hasattr(self, 'bollinger_indicator_items') and self.bollinger_indicator_items:
                for item in self.bollinger_indicator_items:
                    self.bollinger_width_chart_widget.removeItem(item)

            # Initialize list to store indicator line items
            self.bollinger_indicator_items = []

            # Calculate the line position (+2 above the bollinger width line peak)
            line_y_position = max_bbw + 2.0

            # Create colored segments based on the same logic as vector 50 chart
            current_segment_color = None
            segment_x = []
            segment_y = []

            # Calculate the EXACT same vector 50 that the vector 50 chart uses
            vector_50 = self.calculate_vector(self.data, 50)

            # Get skip_candles to match main chart visibility
            from parameter_registry import default_registry
            vector_length = default_registry.get_value('vector_length')
            skip_candles = vector_length + 1

            for i, x in enumerate(x_coords):
                # Calculate the actual data index (x is the chart x-coordinate, which starts from skip_candles)
                data_index = x
                if data_index < len(self.data):
                    # Use the EXACT same color logic as vector 50 chart background
                    # This matches PriceBasedVectorItem logic: color based on price position relative to vector
                    color = (139, 0, 0, 255)  # Default to red (EXACT same as vector 50 chart bg_down_color)

                    # Use the EXACT same vector 50 data that the vector 50 chart uses
                    if len(vector_50) > 0 and data_index < len(vector_50):
                        close_price = self.data['Close'].iloc[data_index]
                        vector_price = vector_50.iloc[data_index]

                        # Color based on price position relative to vector (EXACT same as PriceBasedVectorItem)
                        if close_price > vector_price:
                            # Price above vector - green background (EXACT same as vector 50 chart)
                            color = (0, 100, 0, 255)  # EXACT same as bg_up_color=(0, 100, 0, 255)
                        else:
                            # Price below vector - red background (EXACT same as vector 50 chart)
                            color = (139, 0, 0, 255)  # EXACT same as bg_down_color=(139, 0, 0, 255)

                    # If color changed, finish current segment and start new one
                    if current_segment_color != color:
                        # Draw previous segment if it exists and has more than one point
                        if len(segment_x) > 1 and current_segment_color is not None:
                            segment_item = self.bollinger_width_chart_widget.plot(
                                segment_x, segment_y,
                                pen=pg.mkPen(current_segment_color, width=3),  # Thicker line for visibility
                                name='Bollinger Indicator'
                            )
                            self.bollinger_indicator_items.append(segment_item)

                        # Start new segment (include last point for continuity if exists)
                        if segment_x and segment_y:
                            segment_x = [segment_x[-1], x]  # Include last point for continuity
                            segment_y = [segment_y[-1], line_y_position]
                        else:
                            segment_x = [x]
                            segment_y = [line_y_position]
                        current_segment_color = color
                    else:
                        # Continue current segment
                        segment_x.append(x)
                        segment_y.append(line_y_position)

            # Draw the final segment if it has more than one point
            if len(segment_x) > 1 and current_segment_color is not None:
                final_item = self.bollinger_width_chart_widget.plot(
                    segment_x, segment_y,
                    pen=pg.mkPen(current_segment_color, width=3),  # Thicker line for visibility
                    name='Bollinger Indicator'
                )
                self.bollinger_indicator_items.append(final_item)

        except Exception as e:
            print(f"Error adding Bollinger indicator line: {e}")
            import traceback
            traceback.print_exc()

    def add_bollinger_reference_lines(self, max_bbw):
        """Add horizontal reference lines at 25%, 50%, and 75% of the peak Bollinger width"""
        try:
            # Clear any existing reference lines
            if hasattr(self, 'bollinger_reference_lines') and self.bollinger_reference_lines:
                for line in self.bollinger_reference_lines:
                    self.bollinger_width_chart_widget.removeItem(line)

            # Initialize list to store reference lines
            self.bollinger_reference_lines = []

            # Calculate reference levels (fixed 0.1 level + 25%, 50%, 75%, 90% of peak)
            reference_levels = [
                0.1,  # Fixed level at 0.1
                0.25 * max_bbw,  # 25% of peak
                0.50 * max_bbw,  # 50% of peak
                0.75 * max_bbw,  # 75% of peak
                0.90 * max_bbw   # 90% of peak
            ]

            # Get the current X range to position labels at the right edge
            x_range = self.bollinger_width_chart_widget.viewRange()[0]
            x_max = x_range[1] if len(x_range) > 1 else 100  # Fallback to 100 if no range

            # Add horizontal lines for each reference level
            for level_value in reference_levels:
                # Create horizontal line
                ref_line = pg.InfiniteLine(
                    pos=level_value,
                    angle=0,  # Horizontal line
                    movable=False,
                    pen=pg.mkPen(color='#888888', width=1, style=QtCore.Qt.PenStyle.DashLine)
                )

                # Add line to chart
                self.bollinger_width_chart_widget.addItem(ref_line)
                self.bollinger_reference_lines.append(ref_line)

                # Add text label showing the actual Y coordinate value
                ref_label = pg.TextItem(
                    text=f"{level_value:.2f}",  # Show actual Y coordinate with 2 decimal places
                    color='#888888',
                    anchor=(1, 0.5)  # Right-aligned, vertically centered
                )
                ref_label.setPos(x_max, level_value)  # Position at right edge of chart
                self.bollinger_width_chart_widget.addItem(ref_label)
                self.bollinger_reference_lines.append(ref_label)

        except Exception as e:
            print(f"Error adding bollinger reference lines: {e}")
            import traceback
            traceback.print_exc()

    def calculate_average_peak_trough_prices(self):
        """Calculate average peak and trough prices from the cached pivot transitions data"""
        if not hasattr(self, '_cached_pivot_transitions') or not self._cached_pivot_transitions:
            return None, None

        # Extract peaks (bullish cycles) and troughs (bearish cycles) from cached data
        peaks = []
        troughs = []

        for level, cycle_type, is_closed in self._cached_pivot_transitions:
            if cycle_type == "bullish" and level > 0:  # Peaks are positive bullish levels
                peaks.append(level)
            elif cycle_type == "bearish" and level < 0:  # Troughs are negative bearish levels
                troughs.append(level)

        avg_peak_price = None
        avg_trough_price = None

        if peaks and len(peaks) > 0:
            # Convert percentage values back to actual prices using current pivot
            if hasattr(self, 'current_pivot') and self.current_pivot:
                # Convert to pandas Series for robust data validation
                peaks_series = pd.Series(peaks)

                # Convert to numeric values, coercing errors to NaN
                peaks_series = pd.to_numeric(peaks_series, errors='coerce')

                # Drop any NaN or infinite values
                peaks_series = peaks_series.dropna()
                peaks_series = peaks_series[np.isfinite(peaks_series)]

                if len(peaks_series) > 0:
                    peak_prices = [self.current_pivot * (1 + peak/100) for peak in peaks_series]
                    # Use numpy's mean for high precision calculation
                    avg_peak_price = np.mean(peak_prices)

        if troughs and len(troughs) > 0:
            # Convert percentage values back to actual prices using current pivot
            if hasattr(self, 'current_pivot') and self.current_pivot:
                # Convert to pandas Series for robust data validation
                troughs_series = pd.Series(troughs)

                # Convert to numeric values, coercing errors to NaN
                troughs_series = pd.to_numeric(troughs_series, errors='coerce')

                # Drop any NaN or infinite values
                troughs_series = troughs_series.dropna()
                troughs_series = troughs_series[np.isfinite(troughs_series)]

                if len(troughs_series) > 0:
                    trough_prices = [self.current_pivot * (1 + trough/100) for trough in troughs_series]
                    # Use numpy's mean for high precision calculation
                    avg_trough_price = np.mean(trough_prices)

        return avg_peak_price, avg_trough_price

    def find_previous_peak_or_trough(self):
        """Find the previous (second-to-last) peak or trough and return its index and price"""
        if not hasattr(self, '_cached_rays_info') or not self._cached_rays_info:
            return None, None, None

        # rays_info format: (extreme_index, extreme_value, ray_end, current_cycle, is_closed, count)
        # Sort by extreme_index to get chronological order
        sorted_rays = sorted(self._cached_rays_info, key=lambda x: x[0])

        if len(sorted_rays) < 2:
            # Need at least 2 rays to have a "previous" one
            return None, None, None

        # Get the second-to-last ray (previous peak/trough)
        prev_ray = sorted_rays[-2]
        extreme_index, extreme_value, ray_end, cycle_type, is_closed, count = prev_ray

        # Determine if it's a peak or trough
        extrema_type = "peak" if cycle_type == "bullish" else "trough"

        # Convert percentage to actual price
        if hasattr(self, 'current_pivot') and self.current_pivot:
            extrema_price = self.current_pivot * (1 + extreme_value/100)
            return extrema_type, extrema_price, extreme_index

        return None, None, None

    def calculate_average_peak_trough_lengths(self):
        """Calculate average lengths (durations) of peaks and troughs from rays_info data"""
        if not hasattr(self, '_cached_rays_info') or not self._cached_rays_info:
            return None, None

        # Extract peak and trough lengths from rays_info
        peak_lengths = []
        trough_lengths = []

        for extreme_index, extreme_value, ray_end, cycle_type, is_closed, count in self._cached_rays_info:
            if cycle_type == "bullish":  # Peak
                peak_lengths.append(count)
            elif cycle_type == "bearish":  # Trough
                trough_lengths.append(count)

        # Calculate robust averages with data validation
        if peak_lengths:
            # Convert to pandas Series for robust data validation
            peak_lengths_series = pd.Series(peak_lengths)

            # Convert to numeric values, coercing errors to NaN
            peak_lengths_series = pd.to_numeric(peak_lengths_series, errors='coerce')

            # Drop any NaN or infinite values
            peak_lengths_series = peak_lengths_series.dropna()
            peak_lengths_series = peak_lengths_series[np.isfinite(peak_lengths_series)]

            avg_peak_length = np.mean(peak_lengths_series) if len(peak_lengths_series) > 0 else None
        else:
            avg_peak_length = None

        if trough_lengths:
            # Convert to pandas Series for robust data validation
            trough_lengths_series = pd.Series(trough_lengths)

            # Convert to numeric values, coercing errors to NaN
            trough_lengths_series = pd.to_numeric(trough_lengths_series, errors='coerce')

            # Drop any NaN or infinite values
            trough_lengths_series = trough_lengths_series.dropna()
            trough_lengths_series = trough_lengths_series[np.isfinite(trough_lengths_series)]

            avg_trough_length = np.mean(trough_lengths_series) if len(trough_lengths_series) > 0 else None
        else:
            avg_trough_length = None

        return avg_peak_length, avg_trough_length

    def create_average_cycle(self):
        """Create a smooth average cycle starting from the previous peak or trough"""
        # Get previous peak/trough information
        prev_type, prev_price, prev_index = self.find_previous_peak_or_trough()
        if prev_type is None or prev_price is None or prev_index is None:
            return None, None

        # Get average peak and trough prices
        avg_peak_price, avg_trough_price = self.calculate_average_peak_trough_prices()
        if avg_peak_price is None or avg_trough_price is None:
            return None, None

        # Get average peak and trough lengths
        avg_peak_length, avg_trough_length = self.calculate_average_peak_trough_lengths()
        if avg_peak_length is None or avg_trough_length is None:
            return None, None

        # Convert prices to percentages for chart display
        if not hasattr(self, 'current_pivot') or self.current_pivot is None:
            return None, None

        prev_price_pct = ((prev_price / self.current_pivot) - 1) * 100
        avg_peak_pct = ((avg_peak_price / self.current_pivot) - 1) * 100
        avg_trough_pct = ((avg_trough_price / self.current_pivot) - 1) * 100

        # Calculate total cycle length
        total_length = int(avg_peak_length + avg_trough_length)

        # Use high resolution for smooth curves (10x more points for smoothness)
        resolution_multiplier = 10
        total_points = total_length * resolution_multiplier + 1

        # Create high-resolution x-coordinates
        x_coords = []
        y_coords = []

        # Generate smooth average cycle with high resolution
        for i in range(total_points):
            # Calculate the actual position in the original timeline
            actual_position = i / resolution_multiplier
            x_coords.append(prev_index + actual_position)

            if prev_type == "trough":
                # Cycle pattern: trough -> peak -> trough
                if actual_position <= avg_peak_length:
                    # Rising phase: trough to peak
                    phase_progress = actual_position / avg_peak_length
                    # Use sine for smooth acceleration/deceleration with perfect curvature
                    smooth_progress = (1 - np.cos(phase_progress * np.pi)) / 2
                    y = prev_price_pct + (avg_peak_pct - prev_price_pct) * smooth_progress
                else:
                    # Falling phase: peak to trough
                    phase_progress = (actual_position - avg_peak_length) / avg_trough_length
                    # Use sine for smooth deceleration/acceleration with perfect curvature
                    smooth_progress = (1 + np.cos(phase_progress * np.pi)) / 2
                    y = avg_peak_pct + (avg_trough_pct - avg_peak_pct) * (1 - smooth_progress)

            else:  # prev_type == "peak"
                # Cycle pattern: peak -> trough -> peak
                if actual_position <= avg_trough_length:
                    # Falling phase: peak to trough
                    phase_progress = actual_position / avg_trough_length
                    # Use sine for smooth acceleration/deceleration with perfect curvature
                    smooth_progress = (1 - np.cos(phase_progress * np.pi)) / 2
                    y = prev_price_pct + (avg_trough_pct - prev_price_pct) * smooth_progress
                else:
                    # Rising phase: trough to peak
                    phase_progress = (actual_position - avg_trough_length) / avg_peak_length
                    # Use sine for smooth deceleration/acceleration with perfect curvature
                    smooth_progress = (1 + np.cos(phase_progress * np.pi)) / 2
                    y = avg_trough_pct + (avg_peak_pct - avg_trough_pct) * (1 - smooth_progress)

            y_coords.append(y)

        return x_coords, y_coords

    def clear_white_lines(self):
        """Clear existing average cycle from the chart"""
        for line_key, line in self.white_lines.items():
            if line is not None:
                try:
                    self.plot_widget.removeItem(line)
                except:
                    pass
                self.white_lines[line_key] = None

    def draw_white_lines(self):
        """Draw the average cycle if enabled in settings"""
        # Clear existing white lines first
        self.clear_white_lines()

        # Check if average cycle should be shown based on settings
        display_options = self.settings_dialog.get_display_options() if hasattr(self, 'settings_dialog') else {}
        if not display_options.get('show_average_cycle', True):
            return

        # Draw average cycle
        x_coords, y_coords = self.create_average_cycle()
        if x_coords is not None and y_coords is not None:
            self.white_lines['average_cycle'] = self.plot_widget.plot(
                x_coords, y_coords,
                pen=pg.mkPen('white', width=2),
                name='Average Cycle'
            )
            print(f"Drew average cycle from index {x_coords[0]} to {x_coords[-1]} ({len(x_coords)} points)")
    def draw_threshold_levels(self, rebased_data, update_labels=True):
        # Remove existing threshold lines
        for line in self.threshold_lines:
            self.plot_widget.removeItem(line)
        self.threshold_lines = []

        # Only remove and reset labels if update_labels is True
        if update_labels:
            for label in self.threshold_labels:
                self.plot_widget.removeItem(label)
            self.threshold_labels = []
        if not rebased_data:
            return

        # The percentage bands are now handled in the update_threshold_labels method
        # This method is kept for compatibility but the actual implementation is moved
        pass
    def update_threshold_labels(self):
        x_range = self.plot_widget.viewRange()[0]
        x_max = x_range[1]
        for text in self.threshold_labels:
            if hasattr(text, 'myThreshold'):
                text.setPos(x_max, text.myThreshold)
    def get_current_extrema_price(self):
        """Get the most recent extrema price (current extrema)"""
        extrema_price = None
        if hasattr(self, 'extrema_prices') and self.extrema_prices:
            # Find the most recent extrema change (the current extrema)
            most_recent_extrema_idx = None
            for extrema_idx, price in self.extrema_prices.items():
                if most_recent_extrema_idx is None or extrema_idx > most_recent_extrema_idx:
                    most_recent_extrema_idx = extrema_idx
                    extrema_price = price

        # Use the current extrema price if available, otherwise fall back to current_pivot
        return extrema_price if extrema_price is not None else self.current_pivot

    def get_pricing_reference(self, level_percentage, current_price=None, vector_price=None):
        """
        Get the appropriate pricing reference based on current price position relative to extrema.

        When price is below the extrema:
        - Peak and trough rays above the extrema use % from vector for pricing

        When price is above the extrema:
        - Peak and trough rays below the extrema use % from extrema for pricing

        Args:
            level_percentage: The percentage level of the ray/level
            current_price: Current market price (if None, will try to get from data)
            vector_price: Current vector price (if None, will try to get from last_vector_price)

        Returns:
            The appropriate price reference (extrema price or vector price)
        """
        # Get current extrema price
        extrema_price = self.get_current_extrema_price()
        if extrema_price is None:
            return extrema_price  # Fallback to extrema if no reference available

        # Get current price if not provided
        if current_price is None:
            if hasattr(self, 'data') and self.data is not None and not self.data.empty:
                current_price = self.data['Close'].iloc[-1]
            else:
                return extrema_price  # Fallback to extrema if no current price

        # Get vector price if not provided
        if vector_price is None:
            if hasattr(self, 'last_vector_price') and self.last_vector_price is not None:
                vector_price = self.last_vector_price
            else:
                return extrema_price  # Fallback to extrema if no vector price

        # Determine if current price is above or below extrema
        price_below_extrema = current_price < extrema_price

        # Determine if the level is above or below extrema (0% represents extrema)
        level_above_extrema = level_percentage > 0

        # Apply the conditional pricing logic
        if price_below_extrema and level_above_extrema:
            # Price is below extrema, and this level is above extrema -> use vector for pricing
            return vector_price
        elif not price_below_extrema and not level_above_extrema:
            # Price is above extrema, and this level is below extrema -> use vector for pricing
            return vector_price
        else:
            # Default case: use extrema for pricing
            return extrema_price

    def draw_cycle_rays(self, rebased_data):
        # Check if peak/trough rays should be shown based on settings
        display_options = self.settings_dialog.get_display_options()
        if not display_options.get('show_peak_trough_rays', True):
            return

        # Also update enhanced trendlines if enabled
        if hasattr(self, 'show_enhanced_trendlines') and self.show_enhanced_trendlines.isChecked():
            self.update_enhanced_trendlines()
        if hasattr(self, 'cycle_rays'):
            for ray in self.cycle_rays:
                self.plot_widget.removeItem(ray)
        self.cycle_rays = []
        if hasattr(self, 'cycle_ray_labels'):
            for label in self.cycle_ray_labels:
                self.plot_widget.removeItem(label)
        self.cycle_ray_labels = []
        if hasattr(self, 'cycle_extreme_labels'):
            for label in self.cycle_extreme_labels:
                self.plot_widget.removeItem(label)
        self.cycle_extreme_labels = []
        if not rebased_data:
            return

        # Get skip_candles value to only process visible candles
        from parameter_registry import default_registry
        vector_length = default_registry.get_value('vector_length')
        skip_candles = vector_length + 1

        # Filter rebased_data to only include visible candles (those at index >= skip_candles)
        visible_rebased_data = []
        for i, (x, op, hi, lo, cl) in enumerate(rebased_data):
            # x represents the original index, only include if x >= skip_candles
            if x >= skip_candles:
                visible_rebased_data.append((x, op, hi, lo, cl))

        if not visible_rebased_data:
            return

        current_cycle = None
        extreme_value = None
        extreme_index = None
        cycle_start = None
        rays_info = []
        pivot_transitions = []
        for i, (x, op, hi, lo, cl) in enumerate(visible_rebased_data):
            cycle = "bullish" if cl >= 0 else "bearish"
            if current_cycle is None:
                current_cycle = cycle
                extreme_index = x
                extreme_value = hi if cycle == "bullish" else lo
                cycle_start = x
            else:
                if cycle == current_cycle:
                    if current_cycle == "bullish" and hi > extreme_value:
                        extreme_value = hi
                        extreme_index = x
                    elif current_cycle == "bearish" and lo < extreme_value:
                        extreme_value = lo
                        extreme_index = x
                else:
                    count = extreme_index - cycle_start + 1
                    ray_end = None
                    is_closed = False
                    # Find the index of the extreme in visible_rebased_data
                    extreme_data_index = None
                    for idx, (x_val, _, _, _, _) in enumerate(visible_rebased_data):
                        if x_val == extreme_index:
                            extreme_data_index = idx
                            break

                    if extreme_data_index is not None:
                        # Check candles after the extreme for closing conditions
                        # Add a small tolerance for floating-point precision issues
                        tolerance = 0.0001  # 0.01% tolerance

                        for j in range(extreme_data_index + 1, len(visible_rebased_data)):
                            x_j, _, hi_j, lo_j, _ = visible_rebased_data[j]

                            # Debug logging
                            if current_cycle == "bullish":
                                print(f"Checking bullish peak closure: candle {x_j}, hi_j={hi_j:.4f}, extreme_value={extreme_value:.4f}")
                                # For peaks, close when any subsequent high equals or exceeds the peak (with tolerance)
                                if hi_j >= (extreme_value - tolerance):
                                    ray_end = x_j
                                    is_closed = True
                                    print(f"Peak closed at candle {x_j}: hi_j={hi_j:.4f} >= extreme_value={extreme_value:.4f} (tolerance: {tolerance})")
                                    break
                            else:  # bearish cycle
                                print(f"Checking bearish trough closure: candle {x_j}, lo_j={lo_j:.4f}, extreme_value={extreme_value:.4f}")
                                # For troughs, close when any subsequent low equals or goes below the trough (with tolerance)
                                if lo_j <= (extreme_value + tolerance):
                                    ray_end = x_j
                                    is_closed = True
                                    print(f"Trough closed at candle {x_j}: lo_j={lo_j:.4f} <= extreme_value={extreme_value:.4f} (tolerance: {tolerance})")
                                    break
                    if ray_end is None:
                        ray_end = visible_rebased_data[-1][0]
                        is_closed = False
                    rays_info.append((extreme_index, extreme_value, ray_end, current_cycle, is_closed, count))
                    pivot_transitions.append((extreme_value, current_cycle, is_closed))
                    current_cycle = cycle
                    extreme_index = x
                    extreme_value = hi if cycle == "bullish" else lo
                    cycle_start = x
        if current_cycle is not None:
            count = extreme_index - cycle_start + 1
            ray_end = None
            is_closed = False
            # Find the index of the extreme in visible_rebased_data
            extreme_data_index = None
            for idx, (x_val, _, _, _, _) in enumerate(visible_rebased_data):
                if x_val == extreme_index:
                    extreme_data_index = idx
                    break

            if extreme_data_index is not None:
                # Check candles after the extreme for closing conditions
                # Add a small tolerance for floating-point precision issues
                tolerance = 0.0001  # 0.01% tolerance

                for j in range(extreme_data_index + 1, len(visible_rebased_data)):
                    x_j, _, hi_j, lo_j, _ = visible_rebased_data[j]

                    # Debug logging
                    if current_cycle == "bullish":
                        print(f"Checking final bullish peak closure: candle {x_j}, hi_j={hi_j:.4f}, extreme_value={extreme_value:.4f}")
                        # For peaks, close when any subsequent high equals or exceeds the peak (with tolerance)
                        if hi_j >= (extreme_value - tolerance):
                            ray_end = x_j
                            is_closed = True
                            print(f"Final peak closed at candle {x_j}: hi_j={hi_j:.4f} >= extreme_value={extreme_value:.4f} (tolerance: {tolerance})")
                            break
                    else:  # bearish cycle
                        print(f"Checking final bearish trough closure: candle {x_j}, lo_j={lo_j:.4f}, extreme_value={extreme_value:.4f}")
                        # For troughs, close when any subsequent low equals or goes below the trough (with tolerance)
                        if lo_j <= (extreme_value + tolerance):
                            ray_end = x_j
                            is_closed = True
                            print(f"Final trough closed at candle {x_j}: lo_j={lo_j:.4f} <= extreme_value={extreme_value:.4f} (tolerance: {tolerance})")
                            break

            if ray_end is None:
                ray_end = visible_rebased_data[-1][0]
                is_closed = False
            rays_info.append((extreme_index, extreme_value, ray_end, current_cycle, is_closed, count))
            pivot_transitions.append((extreme_value, current_cycle, is_closed))
        # All rays in rays_info are now from visible candles only
        rays_to_draw = rays_info

        for start, level, end, cycle_type, is_closed, count in rays_to_draw:
            color = 'g' if cycle_type == "bullish" else 'r'
            ray_line = self.plot_widget.plot(
                [start, end], [level, level],
                pen=pg.mkPen(color=color, width=2, style=QtCore.Qt.PenStyle.DashLine)
            )
            self.cycle_rays.append(ray_line)
            if self.current_pivot is not None:
                if is_closed:
                    text_content = "closed"
                else:
                    # Use conditional pricing logic based on current price position relative to extrema
                    pricing_reference = self.get_pricing_reference(level)

                    # Don't modify self.current_pivot here to avoid inconsistency with P/T imprints
                    # Use the pricing_reference directly for calculations

                    if pricing_reference is not None:
                        actual_price = pricing_reference * (1 + level / 100)

                        # Get vector price for reference type determination
                        vector_price = None
                        if hasattr(self, '_plot_data_cache') and self._plot_data_cache.get('vector') is not None:
                            vector = self._plot_data_cache.get('vector')
                            if len(vector) > 0:
                                vector_price = vector.iloc[-1]
                        elif hasattr(self, 'last_vector_price') and self.last_vector_price is not None:
                            vector_price = self.last_vector_price

                        # Determine reference type indicator
                        reference_type = "V" if pricing_reference == vector_price else "E"

                        # Only show (Target) for untouched (open) peaks and troughs
                        text_content = f"{actual_price:,.2f} (Target)"
                    else:
                        text_content = f"{level:.2f}% (Target)"
                if is_closed:
                    text_item = pg.TextItem(
                        html=f'<div style="background-color: rgba(0,0,0,0.75); border: 1px solid {color}; padding: 2px; font-size: 9pt; color: white;">{text_content}</div>',
                        anchor=(0, 0.5)
                    )
                else:
                    text_item = pg.TextItem(
                        html=f'<div style="background-color: rgba(0,0,0,0.75); border: 1px solid {color}; padding: 2px; font-size: 9pt; font-weight: bold; color: white;">{text_content}</div>',
                        anchor=(0, 0.5)
                    )
                text_item.setPos(end, level)
                self.plot_widget.addItem(text_item)
                self.cycle_ray_labels.append(text_item)
                offset = 0.1 if cycle_type == "bullish" else -0.1
                extreme_text = "H" if cycle_type == "bullish" else "L"
                extreme_text = f"{extreme_text} ({count})"
                extreme_label = pg.TextItem(text=extreme_text, color=color, anchor=(0.5, 0.5))
                extreme_label.setPos(start, level + offset)
                self.plot_widget.addItem(extreme_label)
                self.cycle_extreme_labels.append(extreme_label)
        # Also remove first peak/trough from cached data
        pivot_transitions_to_cache = pivot_transitions[1:] if len(pivot_transitions) > 1 else []
        rays_info_to_cache = rays_info[1:] if len(rays_info) > 1 else []

        self._cached_pivot_transitions = pivot_transitions_to_cache
        self._cached_rays_info = rays_info_to_cache  # Store rays_info for white lines
        self.draw_imprints(pivot_transitions_to_cache)
    def draw_imprints(self, pivot_transitions):
        # Check if imprints should be shown based on settings
        display_options = self.settings_dialog.get_display_options()
        if not display_options.get('show_imprints', True):
            return

        # Import the EnhancedVectorDisplay if not already imported
        if not hasattr(self, 'vector_display'):
            self.vector_display = None

        # Clear existing items
        if hasattr(self, 'imprint_lines'):
            for line in self.imprint_lines:
                self.imprints_widget.removeItem(line)
        if hasattr(self, 'imprint_labels'):
            for label in self.imprint_labels:
                self.imprints_widget.removeItem(label)
        if hasattr(self, 'imprint_clusters'):
            for cluster in self.imprint_clusters:
                self.plot_widget.removeItem(cluster)

        # Clear enhanced vector display if available
        if hasattr(self, 'vector_display') and self.vector_display:
            self.vector_display.clear()

        self.imprint_lines = []
        self.imprint_labels = []
        self.imprint_clusters = []

        if not pivot_transitions:
            return

        self.imprints_widget.clear()
        x_range = self.imprints_widget.viewRange()[0]
        left_edge = x_range[0]
        right_edge = x_range[1]

        # Add modern styled title
        title = pg.TextItem(
            html='''
            <div style="
                color: #FFFFFF;
                font-size: 10pt;
                font-weight: bold;
                text-shadow: 0px 1px 2px rgba(0,0,0,0.7);
            ">
                P/T
            </div>
            ''',
            anchor=(0.5, 0)
        )
        title.setPos((left_edge + right_edge) / 2, self.plot_widget.viewRange()[1][1])
        self.imprints_widget.addItem(title)
        self.imprint_labels.append(title)

        # Process transitions and clusters
        sorted_transitions = sorted(pivot_transitions, key=lambda x: x[0])
        cluster_threshold = self.settings_dialog.cluster_threshold_spin.value() if hasattr(self, 'settings_dialog') else 0.15
        min_cluster_size = self.settings_dialog.min_cluster_size_spin.value() if hasattr(self, 'settings_dialog') else 3
        show_clusters = self.show_clusters.isChecked() and self.settings_dialog.enable_clusters.isChecked() if hasattr(self, 'settings_dialog') else self.show_clusters.isChecked()
        cluster_color = self.settings_dialog.cluster_color_combo.currentText() if hasattr(self, 'settings_dialog') else "yellow"
        cluster_opacity = self.settings_dialog.cluster_opacity_spin.value() if hasattr(self, 'settings_dialog') else 50

        # Group transitions into clusters
        clusters = []
        current_cluster = []
        for i, (level, cycle_type, is_closed) in enumerate(sorted_transitions):
            if not current_cluster:
                current_cluster.append((level, cycle_type, is_closed))
            else:
                last_level = current_cluster[-1][0]
                if abs(level - last_level) <= cluster_threshold:
                    current_cluster.append((level, cycle_type, is_closed))
                else:
                    if len(current_cluster) >= min_cluster_size:
                        clusters.append(current_cluster)
                    current_cluster = [(level, cycle_type, is_closed)]
        if len(current_cluster) >= min_cluster_size:
            clusters.append(current_cluster)

        # Determine if we should use animations
        # Only animate when there are fewer than 20 transitions to avoid performance issues
        use_animations = len(sorted_transitions) < 20

        # Draw vector lines and labels
        if hasattr(self, 'vector_display') and self.vector_display:
            # Use enhanced vector display
            for level, cycle_type, is_closed in sorted_transitions:
                # Draw vector line with modern styling
                self.vector_display.draw_vector_line(
                    (left_edge, right_edge),
                    level,
                    cycle_type=cycle_type,
                    is_closed=is_closed,
                    animate=use_animations
                )

                # Add price label if pivot is available
                if self.current_pivot is not None:
                    # Use conditional pricing logic based on current price position relative to extrema
                    pricing_reference = self.get_pricing_reference(level)

                    # Get vector price for reference type determination
                    vector_price = None
                    if hasattr(self, '_plot_data_cache') and self._plot_data_cache.get('vector') is not None:
                        vector = self._plot_data_cache.get('vector')
                        if len(vector) > 0:
                            vector_price = vector.iloc[-1]
                    elif hasattr(self, 'last_vector_price') and self.last_vector_price is not None:
                        vector_price = self.last_vector_price

                    # Don't modify self.current_pivot here to avoid inconsistency with peak/trough rays
                    # Use the pricing_reference directly for calculations

                    # Calculate price from percentage using the appropriate reference
                    actual_price = pricing_reference * (1 + level / 100)

                    # Determine reference type indicator
                    reference_type = "V" if pricing_reference == vector_price else "E"

                    # Add reference type to the label (if the vector display supports it)
                    # For now, we'll pass the reference type as part of the price display
                    display_price = f"{actual_price:.2f}{reference_type}"

                    self.vector_display.add_vector_label(
                        ((left_edge + right_edge) / 2, level),
                        display_price,
                        cycle_type=cycle_type,
                        is_closed=is_closed,
                        animate=use_animations
                    )
        else:
            # Fallback to standard display
            for level, cycle_type, is_closed in sorted_transitions:
                # Determine line color based on cycle type
                if cycle_type == "bullish":
                    line_color = "#00FF00" if is_closed else "#90EE90"
                else:
                    line_color = "#FF6666" if is_closed else "#FF3333"

                # Draw vector line
                imprint_line = self.imprints_widget.plot(
                    [left_edge, right_edge], [level, level],
                    pen=pg.mkPen(color=line_color, width=3)
                )
                self.imprint_lines.append(imprint_line)

                # Add price label if pivot is available
                if self.current_pivot is not None:
                    # Use conditional pricing logic based on current price position relative to extrema
                    pricing_reference = self.get_pricing_reference(level)

                    # Get vector price for reference type determination
                    vector_price = None
                    if hasattr(self, '_plot_data_cache') and self._plot_data_cache.get('vector') is not None:
                        vector = self._plot_data_cache.get('vector')
                        if len(vector) > 0:
                            vector_price = vector.iloc[-1]
                    elif hasattr(self, 'last_vector_price') and self.last_vector_price is not None:
                        vector_price = self.last_vector_price

                    # Don't modify self.current_pivot here to avoid inconsistency with peak/trough rays
                    # Use the pricing_reference directly for calculations

                    # Calculate price from percentage using the appropriate reference
                    actual_price = pricing_reference * (1 + level / 100)

                    # Determine reference type indicator
                    reference_type = "V" if pricing_reference == vector_price else "E"
                    price_str = f"{actual_price:,.2f}"

                    # Determine text color
                    if cycle_type == "bullish":
                        text_color = "#00FF00" if is_closed else "#90EE90"
                    else:
                        text_color = "#FF6666" if is_closed else "#FF3333"

                    # Create and add label - only show (Target) for untouched (open) P/T
                    label_text = f"{price_str} (Target){reference_type}" if not is_closed else f"{price_str}{reference_type}"
                    label = pg.TextItem(
                        html=f'<div style="background-color: rgba(0,0,0,0.7); padding: 1px 3px; border-radius: 3px; font-size: 8pt; color: {text_color};">{label_text}</div>',
                        anchor=(0.5, 0.5)
                    )
                    label.setPos((left_edge + right_edge) / 2, level)
                    self.imprints_widget.addItem(label)
                    self.imprint_labels.append(label)

        # Draw clusters if enabled
        if show_clusters:
            if hasattr(self, 'vector_display') and self.vector_display:
                # Use enhanced vector display for clusters
                for cluster in clusters:
                    min_level = min(level for level, _, _ in cluster)
                    max_level = max(level for level, _, _ in cluster)
                    avg_level = sum(level for level, _, _ in cluster) / len(cluster)

                    # Create polygon coordinates
                    x_data = [0, len(self.data), len(self.data), 0, 0]
                    y_data = [min_level, min_level, max_level, max_level, min_level]

                    # Calculate prices if pivot is available
                    if self.current_pivot is not None:
                        # Use conditional pricing logic for cluster min and max levels
                        min_pricing_reference = self.get_pricing_reference(min_level)
                        max_pricing_reference = self.get_pricing_reference(max_level)

                        # Don't modify self.current_pivot here to avoid inconsistency
                        # Use the pricing_references directly for calculations

                        # Calculate prices from percentages using appropriate references
                        min_price = min_pricing_reference * (1 + min_level / 100)
                        max_price = max_pricing_reference * (1 + max_level / 100)

                        # Draw cluster with enhanced styling
                        cluster_rect, cluster_label = self.vector_display.draw_vector_cluster(
                            x_data, y_data, min_level, max_level, avg_level,
                            len(cluster), min_price, max_price,
                            color=cluster_color, opacity=cluster_opacity
                        )

                        # Store for later removal
                        if cluster_rect:
                            self.imprint_clusters.append(cluster_rect)
                        if cluster_label:
                            self.imprint_clusters.append(cluster_label)
            else:
                # Fallback to standard cluster display
                for cluster in clusters:
                    min_level = min(level for level, _, _ in cluster)
                    max_level = max(level for level, _, _ in cluster)
                    avg_level = sum(level for level, _, _ in cluster) / len(cluster)

                    # Create polygon coordinates
                    x_data = [0, len(self.data), len(self.data), 0, 0]
                    y_data = [min_level, min_level, max_level, max_level, min_level]

                    # Color mapping
                    color_map = {
                        "yellow": (255, 255, 0),
                        "red": (255, 0, 0),
                        "green": (0, 255, 0),
                        "blue": (0, 0, 255),
                        "cyan": (0, 255, 255),
                        "magenta": (255, 0, 255)
                    }
                    rgb = color_map.get(cluster_color, (255, 255, 0))

                    # Create and add cluster rectangle
                    cluster_rect = pg.PlotDataItem(
                        x=x_data,
                        y=y_data,
                        pen=pg.mkPen(color=cluster_color, width=2, style=QtCore.Qt.PenStyle.DashLine),
                        fillLevel=min_level,
                        brush=pg.mkBrush(color=(*rgb, cluster_opacity))
                    )

                    # Add label if pivot is available
                    if self.current_pivot is not None:
                        # Use conditional pricing logic for cluster min and max levels
                        min_pricing_reference = self.get_pricing_reference(min_level)
                        max_pricing_reference = self.get_pricing_reference(max_level)

                        # Don't modify self.current_pivot here to avoid inconsistency
                        # Use the pricing_references directly for calculations

                        # Calculate prices from percentages using appropriate references
                        min_price = min_pricing_reference * (1 + min_level / 100)
                        max_price = max_pricing_reference * (1 + max_level / 100)
                        cluster_text = f"Cluster ({len(cluster)}): {min_price:.2f} - {max_price:.2f}"

                        # Create and add label
                        cluster_label = pg.TextItem(
                            html=f'<div style="background-color: rgba(0,0,0,0.7); padding: 2px 4px; border-radius: 3px; font-size: 9pt; color: {cluster_color};">{cluster_text}</div>',
                            anchor=(0, 0.5)
                        )
                        cluster_label.setPos(len(self.data) * 0.95, avg_level)

                        # Add to plot and store for later removal
                        self.plot_widget.addItem(cluster_rect)
                        self.plot_widget.addItem(cluster_label)
                        self.imprint_clusters.extend([cluster_rect, cluster_label])

    def _process_potential_crossing(self, idx, price, vector_price):
        try:
            if not self.below_vector and price < vector_price:
                if idx not in self.potential_crossings:
                    crossing_pct = abs((price / vector_price) - 1) * 100
                    self.potential_crossings[idx] = {
                        'direction': 'down',
                        'price': price,
                        'vector_price': vector_price,
                        'candle_count': 1,
                        'magnitude': crossing_pct,
                        'confirmed': False
                    }
                    print(f"Potential down cross at idx {idx}, magnitude: {crossing_pct:.4f}%")
            elif self.below_vector and price > vector_price:
                if idx not in self.potential_crossings:
                    crossing_pct = abs((price / vector_price) - 1) * 100
                    self.potential_crossings[idx] = {
                        'direction': 'up',
                        'price': price,
                        'vector_price': vector_price,
                        'candle_count': 1,
                        'magnitude': crossing_pct,
                        'confirmed': False
                    }
                    print(f"Potential up cross at idx {idx}, magnitude: {crossing_pct:.4f}%")
            for cross_idx in list(self.potential_crossings.keys()):
                if cross_idx >= idx:
                    continue
                cross_info = self.potential_crossings[cross_idx]
                if cross_info['confirmed']:
                    continue
                still_valid = (cross_info['direction'] == 'down' and price < vector_price) or \
                              (cross_info['direction'] == 'up' and price > vector_price)
                if still_valid:
                    cross_info['candle_count'] += 1
                    current_magnitude = abs((price / vector_price) - 1) * 100
                    cross_info['magnitude'] = max(cross_info['magnitude'], current_magnitude)
                    if cross_info['candle_count'] >= self.confirmation_period and \
                       cross_info['magnitude'] >= self.min_crossing_magnitude:
                        cross_info['confirmed'] = True
                        if cross_info['direction'] == 'down':
                            self.below_vector = True
                            self.crossing_points.append(cross_idx)
                            print(f"Confirmed down cross at idx {cross_idx} (magnitude: {cross_info['magnitude']:.4f}%)")
                        else:
                            self.below_vector = False
                            self.crossing_points.append(cross_idx)
                            print(f"Confirmed up cross at idx {cross_idx} (magnitude: {cross_info['magnitude']:.4f}%)")
                else:
                    del self.potential_crossings[cross_idx]
                    print(f"Rejected {cross_info['direction']} cross at idx {cross_idx} - price returned")
        except Exception as e:
            print(f"Error processing crossing: {str(e)}")

            if hasattr(self, 'vector_price_label') and hasattr(self, '_plot_data_cache'):
                vector = self._plot_data_cache.get('vector')
                if vector is not None and self.current_idx < len(vector):
                    last_vector_price = vector.iloc[self.current_idx]
                    self.vector_price_label.setText(f"The Line: {last_vector_price:.2f}")
            if hasattr(self, 'enable_signal_filter') and self.enable_signal_filter.isChecked():
                active_crossings = [x for x in self.potential_crossings.values()
                                    if not x['confirmed'] and x['candle_count'] < self.confirmation_period]
                if active_crossings and hasattr(self, 'crossing_prediction_label'):
                    latest = max(active_crossings, key=lambda x: x['candle_count'])
                    remaining = self.confirmation_period - latest['candle_count']
                    self.crossing_prediction_label.setText(
                        f"Potential {latest['direction']} cross: {remaining} candles left, magnitude: {latest['magnitude']:.2f}%"
                    )
                    self.crossing_prediction_label.setStyleSheet("color: orange; font-weight: bold;")
        except Exception as e:
            print(f"Error in incremental update: {str(e)}")
            self.plot_data_until(self.current_idx)

    def vector_length_changed(self):
        if not hasattr(self, 'data') or self.data is None:
            return
        if self.data is not None:
            view_range = self.plot_widget.viewRange()
            print("Recalculating vector with new parameters...")
            vector_length = self.vector_length_spin.value()

            # Update the parameter registry
            default_registry.set_value('vector_length', vector_length)

            # Reset extrema_prices when vector length changes to fix ray price calculation issues
            if hasattr(self, 'extrema_prices'):
                self.extrema_prices = {}
                print("Reset extrema_prices when changing vector length to fix ray price calculations")

            vector = self.calculate_vector(self.data, vector_length)
            if self.current_pivot is not None and hasattr(self, 'current_idx') and self.current_idx < len(self.data):
                self.current_pivot = vector.iloc[self.current_idx]
            # Initialize cache if it doesn't exist
            if not hasattr(self, '_plot_data_cache'):
                self._plot_data_cache = SimpleCache(10)
            self._plot_data_cache.put('vector', vector)
            self.plot_data_until(self.current_idx)
            self.plot_widget.setRange(xRange=view_range[0], yRange=view_range[1], padding=0)


    def track_cycle(self, vector):
        """Track the cycle (bullish or bearish) based on vector direction changes

        This method tracks the cycle based on vector direction changes:
        - Bullish cycle: when vector moves higher than previous step
        - Bearish cycle: when vector moves lower than previous step
        - If vector stays constant, maintain the previous cycle

        When the vector changes direction, we set a new pivot price using the vector price
        right before the direction change.

        PineScript Alignment:
        - When vector is moving up (green background in PineScript), we're above the pivot
        - When vector is moving down (red background in PineScript), we're below the pivot

        Args:
            vector: The calculated vector

        Returns:
            list: List of cycle information for each candle (cycle type, index of cycle change, pivot price, vector direction)
        """
        # Check if we have access to a candlestick chart with a vector item
        # This allows us to synchronize the vector direction with the candlestick chart
        candlestick_vector_direction = None
        try:
            # Look for a candlestick chart in the parent window
            from candlestick_chart import VectorItem
            parent = self.parent()
            while parent is not None:
                # Check if the parent has a candlestick_chart attribute
                if hasattr(parent, 'candlestick_chart'):
                    # Check if the candlestick chart has a vector_item
                    if hasattr(parent.candlestick_chart, 'vector_item') and isinstance(parent.candlestick_chart.vector_item, VectorItem):
                        # Get the current direction from the vector item
                        candlestick_vector_direction = parent.candlestick_chart.vector_item.get_current_direction()
                        print(f"Found candlestick chart vector direction: {candlestick_vector_direction}")
                        break
                parent = parent.parent()
        except Exception as e:
            print(f"Error checking for candlestick chart vector: {str(e)}")
            candlestick_vector_direction = None

        if not hasattr(self, 'current_cycle'):
            self.current_cycle = 'bullish'  # Default to bullish for first candle

        if not hasattr(self, 'cycle_changes'):
            self.cycle_changes = []

        # Reset cycle changes for new calculation
        self.cycle_changes = []

        # Initialize vector direction tracking
        # If we have a direction from the candlestick chart, use that
        if candlestick_vector_direction is not None:
            self.vector_direction = candlestick_vector_direction
        elif not hasattr(self, 'vector_direction'):
            self.vector_direction = 'up'  # Default to up for first candle

        # Initialize below_pivot tracking based on vector direction
        # This ensures consistency with the candlestick chart
        if candlestick_vector_direction is not None:
            self.below_pivot = (candlestick_vector_direction == 'down')
        elif not hasattr(self, 'below_pivot'):
            self.below_pivot = False  # Default to above pivot

        # Initialize current pivot price
        if not hasattr(self, 'current_pivot_price') or self.current_pivot_price is None:
            # If we have vector data, use the first vector value as the initial pivot
            if len(vector) > 0:
                self.current_pivot_price = vector.iloc[0]
                print(f"Initializing current_pivot_price to first vector value: {self.current_pivot_price:.2f}")
            else:
                self.current_pivot_price = None

        cycles = []
        prev_vector_value = None
        last_pivot_price = None

        # Process each candle to determine cycle and detect vector direction changes
        for i in range(len(vector)):
            if i == 0:
                # First candle - special handling to make it start at the pivot too
                current_direction = 'up'  # Default to up for first candle

                # Set the first candle as a pivot point with direction change
                # This ensures the first cycle starts at the pivot like subsequent cycles
                first_vector_value = vector.iloc[i]
                cycles.append(('bullish', True, first_vector_value, current_direction))

                # Store the first vector value as the pivot price
                prev_vector_value = first_vector_value
                self.vector_direction = current_direction

                # Add the first index to cycle changes to ensure it's treated as a pivot
                self.cycle_changes.append(i)

                # Set the current pivot price to the first vector value
                self.current_pivot_price = first_vector_value

                # Set the visualization pivot to the first vector value
                if not hasattr(self, 'visualization_pivot') or self.visualization_pivot is None:
                    self.visualization_pivot = first_vector_value

                print(f"First candle: Setting initial pivot at {first_vector_value:.2f}")
            else:
                # Determine vector direction
                current_vector = vector.iloc[i]

                # If we have a direction from the candlestick chart, use that for consistency
                if candlestick_vector_direction is not None and i == len(vector) - 1:
                    # Only use the candlestick direction for the most recent candle
                    current_direction = candlestick_vector_direction
                    print(f"Using candlestick chart vector direction: {current_direction}")
                elif current_vector > prev_vector_value:
                    # Vector moved higher - equivalent to green background in PineScript
                    current_direction = 'up'
                elif current_vector < prev_vector_value:
                    # Vector moved lower - equivalent to red background in PineScript
                    current_direction = 'down'
                else:
                    # Vector stayed the same - maintain previous direction
                    current_direction = self.vector_direction

                # Check if vector direction changed
                direction_changed = current_direction != self.vector_direction

                # Set pivot price when vector direction changes
                pivot_price = None
                if direction_changed:
                    # Use the vector price right before the direction change as the pivot
                    # This is the line price right before we transition above/below the extrema
                    pivot_price = prev_vector_value
                    last_pivot_price = pivot_price

                    if current_direction == 'down':
                        # Vector moved down (red background in PineScript) - rebase below pivot
                        print(f"Vector moved DOWN at idx {i} - Rebasing BELOW pivot {pivot_price:.2f} (line price right before transition)")
                    else:
                        # Vector moved up (green background in PineScript) - rebase above pivot
                        print(f"Vector moved UP at idx {i} - Rebasing ABOVE pivot {pivot_price:.2f} (line price right before transition)")

                    # Store the direction change and the pivot price (line price right before transition)
                    self.cycle_changes.append(i)
                    self.vector_direction = current_direction
                    self.current_pivot_price = pivot_price

                    # Store the extrema price (line price right before we transition above/below the extrema)
                    if not hasattr(self, 'extrema_prices'):
                        self.extrema_prices = {}
                    self.extrema_prices[i] = pivot_price

                # Determine cycle type based on vector direction
                if current_direction == 'up':
                    new_cycle = 'bullish'
                else:
                    new_cycle = 'bearish'

                # Check if cycle changed
                cycle_changed = new_cycle != self.current_cycle
                if cycle_changed:
                    self.current_cycle = new_cycle
                    print(f"Cycle changed to {new_cycle} at index {i}: {prev_vector_value:.2f} -> {current_vector:.2f}")

                # Store cycle information: (cycle type, direction changed, pivot price, current direction)
                cycles.append((new_cycle, direction_changed, pivot_price, current_direction))
                prev_vector_value = current_vector

                # Update below_pivot based on current direction
                # This ensures we're always below pivot when vector is moving down (red background in PineScript)
                # and above pivot when vector is moving up (green background in PineScript)
                self.below_pivot = (current_direction == 'down')

                # If this is the most recent candle and we have a candlestick chart direction,
                # make sure below_pivot matches the candlestick chart's direction
                if candlestick_vector_direction is not None and i == len(vector) - 1:
                    self.below_pivot = (candlestick_vector_direction == 'down')
                    print(f"Setting below_pivot to {self.below_pivot} based on candlestick chart direction")

        # If we haven't set a pivot price yet (no direction changes), use the first vector value
        if last_pivot_price is None and len(vector) > 0:
            last_pivot_price = vector.iloc[0]
            self.current_pivot_price = last_pivot_price

        return cycles

    def plot_data_until(self, end_idx):
        if self.data is None or self.data.empty:
            return
        view_range = self.plot_widget.viewRange()
        self.plot_widget.clear()
        self.imprints_widget.clear()

        # Clear average cycle when chart is cleared
        self.clear_white_lines()

        # Apply trading hours filter to the data
        filtered_data = self.filter_data_by_trading_hours(self.data)

        # If filtering resulted in empty data, show a message and return
        if filtered_data.empty:
            # Add a text item to show that no data is available for the selected trading hours
            text_item = pg.TextItem(
                text="No data available for selected trading hours",
                color='white',
                anchor=(0.5, 0.5)
            )
            text_item.setPos(0, 0)
            self.plot_widget.addItem(text_item)
            return

        # Update end_idx to match the filtered data length
        if end_idx >= len(filtered_data):
            end_idx = len(filtered_data) - 1

        # Use filtered data for all calculations
        timestamps = np.arange(len(filtered_data))
        vector = self.calculate_vector(filtered_data, self.vector_length_spin.value())

        # Update self.data temporarily for calculations (restore later)
        original_data = self.data
        self.data = filtered_data

        # Initialize cache if it doesn't exist
        if not hasattr(self, '_plot_data_cache'):
            self._plot_data_cache = SimpleCache(10)
        self._plot_data_cache.put('vector', vector)

        # Clear imprint cache when data changes to improve performance
        if hasattr(self, '_imprint_cache'):
            self._imprint_cache.clear()
            print("Cleared imprint cache for better performance")
        if len(vector) > 0:
            if self.current_pivot is None:
                self.current_pivot = vector.iloc[0]
                self.visualization_pivot = self.current_pivot  # Initialize visualization_pivot
                self.last_vector_price = self.current_pivot
                self.last_pivot_price = self.current_pivot
                self.pivot_crossed = True

        # Track cycles based on line movement
        cycles = self.track_cycle(vector)

        # Ensure the first cycle is properly handled
        if len(cycles) > 0 and cycles[0][1]:  # If first candle is marked as a pivot point
            # Make sure the visualization pivot is set to the first vector value
            self.visualization_pivot = cycles[0][2]  # Use the pivot price from the first cycle
            print(f"Setting initial visualization pivot to {self.visualization_pivot:.2f} for first cycle")

        # Store rebased data as class attribute so it can be accessed by other methods
        self.rebased_data = []
        self.rebased_vector = []

        # Initialize pivot for rebasing
        self.visualization_pivot = self.current_pivot

        # Get vector length from parameter registry to determine how many candles to skip
        from parameter_registry import default_registry
        vector_length = default_registry.get_value('vector_length')
        skip_candles = vector_length + 1

        # Track that first (vector_length + 1) rows are used for distance calculations but not displayed
        # The first skip_candles rows are loaded but not used in the system, they are invisible
        # The category starts at the (skip_candles + 1)th row since the first skip_candles rows are NOT used anywhere
        # These skip_candles rows will be used for distance calculations so it does not START AT 0
        print(f"Skipping first {skip_candles} candles (vector_length: {vector_length} + 1) for chart display")

        # Train classifier if needed and auto-training is enabled
        # Skip this when called from settings dialog (will be handled by ML Tools)
        if (hasattr(self, '_skip_ml_calculations') and self._skip_ml_calculations):
            print("Skipping classifier training as requested")
        elif (self.settings_dialog.enable_classifier.isChecked() and
              self.settings_dialog.auto_train_classifier.isChecked() and
              len(self.crossing_points) >= 10):
            self.crossing_classifier.train(self.data, vector, self.crossing_points)

        confirmed_pivots = []
        rejected_crossings = []
        temp_current_idx = self.current_idx
        temp_below_vector = self.below_vector

        # Always use current_pivot for visualization to ensure proper rebasing
        self.visualization_pivot = self.current_pivot

        # Special handling for the first candle
        # If we have cycles and the first cycle is marked as a pivot point
        if len(cycles) > 0 and cycles[0][1]:
            # Use the first vector value as the visualization pivot
            first_pivot = cycles[0][2]
            if first_pivot is not None:
                self.visualization_pivot = first_pivot
                print(f"Using first candle pivot {self.visualization_pivot:.2f} for rebasing")
        else:
            print(f"Using pivot {self.visualization_pivot:.2f} for rebasing")

        # Initialize crossing_pivots dictionary if it doesn't exist
        if not hasattr(self, 'crossing_pivots'):
            self.crossing_pivots = {}
        for idx in list(self.potential_crossings.keys()):
            if end_idx - idx > 20:
                del self.potential_crossings[idx]
        for idx, (timestamp, row) in enumerate(self.data.iterrows()):
            if idx > end_idx:
                break
            if idx >= skip_candles:
                self.current_idx = idx
                if idx <= end_idx:
                    vector_price = vector.iloc[idx]
                    price = row['Close']

                    # Check if we need to rebase based on vector direction changes
                    if idx < len(cycles):
                        # Get the cycle information for this candle
                        direction_changed = cycles[idx][1]  # Whether vector direction changed at this index
                        pivot_price = cycles[idx][2]  # Get the pivot price (vector price before direction change)
                        vector_direction = cycles[idx][3]  # Get the current vector direction (up/down)

                        # Update below_vector based on the current vector direction
                        # This ensures we're always below pivot when vector is moving down (red background in PineScript)
                        # and above pivot when vector is moving up (green background in PineScript)
                        temp_below_vector = (vector_direction == 'down')

                        # ONLY rebase when the vector direction changes (from up to down or from down to up)
                        # This is the key fix - we only rebase at direction changes, not at price crossings
                        if direction_changed and pivot_price is not None:
                            # Update the visualization pivot
                            self.visualization_pivot = pivot_price
                            print(f"Vector direction changed at idx {idx} - Rebasing with pivot {self.visualization_pivot:.2f}")

                            # Add to crossing points for visualization
                            if idx not in self.crossing_points:
                                self.crossing_points.append(idx)

                            # Store the pivot price for this crossing point
                            self.crossing_pivots[idx] = pivot_price
                            print(f"DEBUG: Stored extrema price {pivot_price:.2f} (line price right before transition) for vector direction change at index {idx}")

                            # Update current pivot
                            self.current_pivot = pivot_price
                            self.pivot_crossed = True

                            # Log the direction change
                            if vector_direction == 'down':
                                print(f"Vector moved DOWN - Setting below_vector to TRUE (PineScript: red background)")
                            else:
                                print(f"Vector moved UP - Setting below_vector to FALSE (PineScript: green background)")

                    # Skip all other rebasing logic that's not related to vector direction changes
                    # We'll keep the code below for compatibility, but we'll modify it to not change the pivot
                    if hasattr(self, 'enable_signal_filter') and self.enable_signal_filter.isChecked():
                        if not temp_below_vector and price < vector_price:
                            # Potential down crossing
                            crossing_pct = abs((price / vector_price) - 1) * 100

                            # First, check if there's a gap
                            if idx > 0 and self.data['Open'].iloc[idx] != self.data['Close'].iloc[idx-1]:
                                # There's a gap - use the previous close (the gap) as the pivot
                                gap_size = self.data['Open'].iloc[idx] - self.data['Close'].iloc[idx-1]
                                gap_pct = (gap_size / self.data['Close'].iloc[idx-1]) * 100

                                # The pivot is the previous close (the gap)
                                pivot_price = self.data['Close'].iloc[idx-1]

                                if gap_size > 0:  # Gap up
                                    print(f"DOWN CROSS WITH UP GAP: {gap_pct:.2f}% ({gap_size:.2f} points)")
                                    print(f"Using previous close as pivot: {pivot_price:.2f}")
                                else:  # Gap down
                                    print(f"DOWN CROSS WITH DOWN GAP: {abs(gap_pct):.2f}% ({abs(gap_size):.2f} points)")
                                    print(f"Using previous close as pivot: {pivot_price:.2f}")
                            else:
                                # No gap - use the open of the candle
                                pivot_price = self.data['Open'].iloc[idx]
                                print(f"No gap detected, using open as pivot: {pivot_price:.2f}")

                            # Don't change the visualization pivot for price crossings
                            # Only track the crossing for signal generation
                            confirmed_pivots.append(idx)
                            print(f"DOWN CROSS DETECTED at idx {idx} - NOT rebasing (only vector direction changes cause rebasing)")
                            # Don't update current_pivot
                            # self.current_pivot = pivot_price
                            # self.pivot_crossed = True
                            temp_below_vector = True

                        # Bearish to bullish: when price gets higher than vector price
                        elif temp_below_vector and price > vector_price:
                            if idx not in self.crossing_points:
                                self.crossing_points.append(idx)
                            # For an up cross (bearish to bullish), we want to use the previous close as the pivot if there's a gap
                            # Otherwise, use the open of the candle

                            # First, check if there's a gap
                            if idx > 0 and self.data['Open'].iloc[idx] != self.data['Close'].iloc[idx-1]:
                                # There's a gap - use the previous close (the gap) as the pivot
                                gap_size = self.data['Open'].iloc[idx] - self.data['Close'].iloc[idx-1]
                                gap_pct = (gap_size / self.data['Close'].iloc[idx-1]) * 100

                                # The pivot is the previous close (the gap)
                                pivot_price = self.data['Close'].iloc[idx-1]

                                if gap_size > 0:  # Gap up
                                    print(f"UP CROSS WITH UP GAP: {gap_pct:.2f}% ({gap_size:.2f} points)")
                                    print(f"Using previous close as pivot: {pivot_price:.2f}")
                                else:  # Gap down
                                    print(f"UP CROSS WITH DOWN GAP: {abs(gap_pct):.2f}% ({abs(gap_size):.2f} points)")
                                    print(f"Using previous close as pivot: {pivot_price:.2f}")
                            else:
                                # No gap - use the open of the candle
                                pivot_price = self.data['Open'].iloc[idx]
                                print(f"No gap detected, using open as pivot: {pivot_price:.2f}")

                            # Don't change the visualization pivot for price crossings
                            # Only track the crossing for signal generation
                            confirmed_pivots.append(idx)
                            print(f"UP CROSS DETECTED at idx {idx} - NOT rebasing (only vector direction changes cause rebasing)")
                            # Don't update current_pivot
                            # self.current_pivot = pivot_price
                            # self.pivot_crossed = True
                            temp_below_vector = False

                    # Normal signal filter logic for other presets
                    elif hasattr(self, 'enable_signal_filter') and self.enable_signal_filter.isChecked():
                        if not temp_below_vector and price < vector_price:
                            # Potential down crossing
                            crossing_pct = abs((price / vector_price) - 1) * 100

                            # Use the adaptive confirmation system if enabled
                            crossing_confirmed = False
                            if hasattr(self.settings_dialog, 'enable_adaptive_confirmation') and self.settings_dialog.enable_adaptive_confirmation.isChecked():
                                print(f"\n*** ADAPTIVE CONFIRMATION ENABLED - Processing DOWN crossing at idx {idx} ***")
                                crossing_confirmed = self.adaptive_confirmation.process_potential_crossing(
                                    self.data, idx, price, vector_price, 'down', crossing_pct
                                )
                                # Update UI to show adaptive confirmation is being used
                                if hasattr(self, 'crossing_prediction_label'):
                                    self.crossing_prediction_label.setText(f"Adaptive Confirmation ACTIVE - Processing crossing at idx {idx}")
                                    self.crossing_prediction_label.setStyleSheet("color: cyan; font-weight: bold;")
                            else:
                                pass

                            # For compatibility with existing code
                            if idx not in self.potential_crossings:
                                self.potential_crossings[idx] = {
                                    'direction': 'down',
                                    'price': price,
                                    'vector_price': vector_price,
                                    'candle_count': 1,
                                    'magnitude': crossing_pct,
                                    'confirmed': crossing_confirmed
                                }
                                print(f"Potential down cross at idx {idx}, magnitude: {crossing_pct:.4f}%")

                                # If immediately confirmed by adaptive system
                                if crossing_confirmed:
                                    temp_below_vector = True
                                    print(f"Immediate confirmation of down cross at idx {idx} by adaptive system")

                        elif temp_below_vector and price > vector_price:
                            # Potential up crossing
                            crossing_pct = abs((price / vector_price) - 1) * 100

                            # Use the adaptive confirmation system if enabled
                            crossing_confirmed = False
                            if hasattr(self.settings_dialog, 'enable_adaptive_confirmation') and self.settings_dialog.enable_adaptive_confirmation.isChecked():
                                print(f"\n*** ADAPTIVE CONFIRMATION ENABLED - Processing UP crossing at idx {idx} ***")
                                crossing_confirmed = self.adaptive_confirmation.process_potential_crossing(
                                    self.data, idx, price, vector_price, 'up', crossing_pct
                                )
                                # Update UI to show adaptive confirmation is being used
                                if hasattr(self, 'crossing_prediction_label'):
                                    self.crossing_prediction_label.setText(f"Adaptive Confirmation ACTIVE - Processing crossing at idx {idx}")
                                    self.crossing_prediction_label.setStyleSheet("color: cyan; font-weight: bold;")
                            else:
                                pass

                            # For compatibility with existing code
                            if idx not in self.potential_crossings:
                                self.potential_crossings[idx] = {
                                    'direction': 'up',
                                    'price': price,
                                    'vector_price': vector_price,
                                    'candle_count': 1,
                                    'magnitude': crossing_pct,
                                    'confirmed': crossing_confirmed
                                }
                                print(f"Potential up cross at idx {idx}, magnitude: {crossing_pct:.4f}%")

                                # If immediately confirmed by adaptive system
                                if crossing_confirmed:
                                    temp_below_vector = False
                                    print(f"Immediate confirmation of up cross at idx {idx} by adaptive system")

                        for cross_idx in list(self.potential_crossings.keys()):
                            if cross_idx >= idx:
                                continue
                            cross_info = self.potential_crossings[cross_idx]
                            if cross_info['confirmed']:
                                continue
                            still_valid = (cross_info['direction'] == 'down' and price < vector_price) or \
                                         (cross_info['direction'] == 'up' and price > vector_price)
                            if still_valid:
                                cross_info['candle_count'] += 1
                                current_magnitude = abs((price / vector_price) - 1) * 100
                                cross_info['magnitude'] = max(cross_info['magnitude'], current_magnitude)

                                # Use the adaptive confirmation system to determine if this crossing should be confirmed
                                crossing_confirmed = False
                                if hasattr(self.settings_dialog, 'enable_adaptive_confirmation') and self.settings_dialog.enable_adaptive_confirmation.isChecked():
                                    print(f"\n*** ADAPTIVE CONFIRMATION ENABLED - Processing EXISTING {cross_info['direction']} crossing at idx {cross_idx} ***")
                                    crossing_confirmed = self.adaptive_confirmation.process_potential_crossing(
                                        self.data, cross_idx, price, vector_price, cross_info['direction'], current_magnitude
                                    )
                                    # Update UI to show adaptive confirmation is being used
                                    if hasattr(self, 'crossing_prediction_label'):
                                        self.crossing_prediction_label.setText(f"Adaptive Confirmation ACTIVE - Processing existing crossing at idx {cross_idx}")
                                        self.crossing_prediction_label.setStyleSheet("color: magenta; font-weight: bold;")
                                else:
                                    pass

                                if crossing_confirmed:
                                    cross_info['confirmed'] = True
                                    if cross_info['direction'] == 'down':
                                        temp_below_vector = True
                                        # Use the vector price as pivot
                                        pivot_price = cross_info['vector_price']

                                        self.visualization_pivot = pivot_price
                                        confirmed_pivots.append(cross_idx)
                                        self.crossing_points.append(cross_idx)
                                        print(f"Confirmed down cross at idx {cross_idx} with adaptive system (magnitude: {cross_info['magnitude']:.4f}%)")
                                    else:
                                        temp_below_vector = False
                                        # Use the vector price as pivot
                                        pivot_price = cross_info['vector_price']

                                        self.visualization_pivot = pivot_price
                                        confirmed_pivots.append(cross_idx)
                                        self.crossing_points.append(cross_idx)
                                        print(f"Confirmed up cross at idx {cross_idx} with adaptive system (magnitude: {cross_info['magnitude']:.4f}%)")
                                # Also check the traditional confirmation method as a fallback
                                elif cross_info['candle_count'] >= self.confirmation_period and \
                                   cross_info['magnitude'] >= self.min_crossing_magnitude:
                                    cross_info['confirmed'] = True
                                    if cross_info['direction'] == 'down':
                                        temp_below_vector = True
                                        # Use the vector price as pivot
                                        pivot_price = cross_info['vector_price']

                                        self.visualization_pivot = pivot_price
                                        confirmed_pivots.append(cross_idx)
                                        self.crossing_points.append(cross_idx)
                                        print(f"Confirmed down cross at idx {cross_idx} with traditional method (magnitude: {cross_info['magnitude']:.4f}%)")
                                    else:
                                        temp_below_vector = False
                                        # Use the vector price as pivot
                                        pivot_price = cross_info['vector_price']

                                        self.visualization_pivot = pivot_price
                                        confirmed_pivots.append(cross_idx)
                                        self.crossing_points.append(cross_idx)
                                        print(f"Confirmed up cross at idx {cross_idx} with traditional method (magnitude: {cross_info['magnitude']:.4f}%)")

                            else:
                                rejected_crossings.append(cross_idx)
                                print(f"Rejected {cross_info['direction']} cross at idx {cross_idx} - price returned")
                    else:
                        if not temp_below_vector and price < vector_price:
                            if idx not in self.crossing_points:
                                self.crossing_points.append(idx)
                            prediction = None
                            if not hasattr(self, '_skip_ml_calculations') or not self._skip_ml_calculations:
                                if self.settings_dialog.enable_classifier.isChecked() and self.crossing_classifier.is_trained and idx > self.settings_dialog.lookback_spin.value():
                                    prediction_result = self.crossing_classifier.predict(self.data, idx, vector)
                                    if prediction_result:
                                        prediction = prediction_result['prediction']
                            # Always update visualization_pivot and add to confirmed_pivots for down crosses
                            # This ensures the chart rebases correctly when crossing below the vector

                            # Check if there's a gap
                            if idx > 0 and self.data['Open'].iloc[idx] != self.data['Close'].iloc[idx-1]:
                                # There's a gap - use the previous close as the pivot
                                pivot_price = self.data['Close'].iloc[idx-1]
                                print(f"DOWN CROSS: Using previous close as pivot (gap detected): {pivot_price:.2f}")
                            else:
                                # No gap - use the open of the candle
                                pivot_price = self.data['Open'].iloc[idx]
                                print(f"DOWN CROSS: Using open price as pivot (no gap): {pivot_price:.2f}")

                            # Don't change the visualization pivot for price crossings
                            # Only track the crossing for signal generation
                            confirmed_pivots.append(idx)
                            print(f"DOWN CROSS DETECTED at idx {idx} - NOT rebasing (only vector direction changes cause rebasing)")
                            # Don't update current_pivot
                            # self.current_pivot = pivot_price
                            # Store the crossing point for signal generation
                            self.crossing_pivots[idx] = vector_price
                            print(f"DEBUG: Stored vector price {vector_price:.2f} for DOWN CROSS at index {idx} in crossing_pivots dictionary")
                            print(f"DEBUG: crossing_pivots dictionary now contains {len(self.crossing_pivots)} entries")
                            # Don't set pivot_crossed
                            # self.pivot_crossed = True
                            temp_below_vector = True
                        elif temp_below_vector and price > vector_price:
                            if idx not in self.crossing_points:
                                self.crossing_points.append(idx)
                            prediction = None
                            if not hasattr(self, '_skip_ml_calculations') or not self._skip_ml_calculations:
                                if self.settings_dialog.enable_classifier.isChecked() and self.crossing_classifier.is_trained and idx > self.settings_dialog.lookback_spin.value():
                                    prediction_result = self.crossing_classifier.predict(self.data, idx, vector)
                                    if prediction_result:
                                        prediction = prediction_result['prediction']
                            # Always update visualization_pivot and add to confirmed_pivots for up crosses
                            # This ensures the chart rebases correctly when crossing above the vector

                            # Check if there's a gap
                            if idx > 0 and self.data['Open'].iloc[idx] != self.data['Close'].iloc[idx-1]:
                                # There's a gap - use the previous close as the pivot
                                pivot_price = self.data['Close'].iloc[idx-1]
                                print(f"UP CROSS: Using previous close as pivot (gap detected): {pivot_price:.2f}")
                            else:
                                # No gap - use the open of the candle
                                pivot_price = self.data['Open'].iloc[idx]
                                print(f"UP CROSS: Using open price as pivot (no gap): {pivot_price:.2f}")

                            # Don't change the visualization pivot for price crossings
                            # Only track the crossing for signal generation
                            confirmed_pivots.append(idx)
                            print(f"UP CROSS DETECTED at idx {idx} - NOT rebasing (only vector direction changes cause rebasing)")
                            # Don't update current_pivot
                            # self.current_pivot = pivot_price
                            # Store the crossing point for signal generation
                            self.crossing_pivots[idx] = vector_price
                            print(f"DEBUG: Stored vector price {vector_price:.2f} for UP CROSS at index {idx} in crossing_pivots dictionary")
                            print(f"DEBUG: crossing_pivots dictionary now contains {len(self.crossing_pivots)} entries")
                            # Don't set pivot_crossed
                            # self.pivot_crossed = True
                            temp_below_vector = False
            # Calculate rebased prices using the current visualization pivot
            # Special handling for the first candle to ensure it aligns with the vector
            if idx == skip_candles:  # First candle that gets included in rebased data (since we skip first skip_candles rows)
                # For the first candle, ensure both candlestick and vector start at the same relative position
                # Use the vector value as the reference point for the first candle
                first_vector_value = vector.iloc[idx]

                # Calculate the standard rebasing
                open_price = ((row['Open'] / self.visualization_pivot) - 1) * 100
                high_price = ((row['High'] / self.visualization_pivot) - 1) * 100
                low_price = ((row['Low'] / self.visualization_pivot) - 1) * 100
                close_price = ((row['Close'] / self.visualization_pivot) - 1) * 100
                vector_price = ((first_vector_value / self.visualization_pivot) - 1) * 100

                print(f"First candle (idx={skip_candles}): Aligning with vector - O:{open_price:.2f}, H:{high_price:.2f}, L:{low_price:.2f}, C:{close_price:.2f}, V:{vector_price:.2f}")
            else:
                # Use the standard calculation for all other candles
                open_price = ((row['Open'] / self.visualization_pivot) - 1) * 100
                high_price = ((row['High'] / self.visualization_pivot) - 1) * 100
                low_price = ((row['Low'] / self.visualization_pivot) - 1) * 100
                close_price = ((row['Close'] / self.visualization_pivot) - 1) * 100
                vector_price = ((vector.iloc[idx] / self.visualization_pivot) - 1) * 100

            # Store rebased data in class attributes for access by other methods
            if idx <= end_idx:
                # Use original index (like candlestick chart) - let CandlestickItem and VectorItem handle the skipping
                self.rebased_data.append((idx, open_price, high_price, low_price, close_price))
                # Store the rebased vector value (percentage from pivot)
                # vector_price is already a percentage, so we don't need to convert it again
                self.rebased_vector.append(vector_price)
        # Still calculate active crossings but don't update UI
        active_crossings = [x for x in self.potential_crossings.values()
                            if not x['confirmed'] and x['candle_count'] < self.confirmation_period]
        if active_crossings and hasattr(self, 'crossing_prediction_label'):
            latest = max(active_crossings, key=lambda x: x['candle_count'])
            remaining = self.confirmation_period - latest['candle_count']
            # Update the label text but don't display it (for functionality)
            self.crossing_prediction_label.setText(
                f"Potential {latest['direction']} cross: {remaining} candles left, magnitude: {latest['magnitude']:.2f}%"
            )
            self.crossing_prediction_label.setStyleSheet("color: orange; font-weight: bold;")
        self.current_idx = temp_current_idx
        self.below_vector = temp_below_vector
        if confirmed_pivots:
            pivot_idx = confirmed_pivots[-1]

            # Use the vector price as pivot
            real_pivot_price = vector.iloc[pivot_idx]

            self.current_pivot = real_pivot_price
            self.visualization_pivot = real_pivot_price  # Update visualization_pivot when pivot changes
        else:
            real_pivot_price = self.current_pivot

        # Get the most recent extrema price for display
        display_pivot_price = real_pivot_price
        if hasattr(self, 'extrema_prices') and self.extrema_prices:
            # Find the most recent extrema change before end_idx
            most_recent_extrema_idx = None
            extrema_price = None
            for extrema_idx, price in self.extrema_prices.items():
                if extrema_idx <= end_idx and (most_recent_extrema_idx is None or extrema_idx > most_recent_extrema_idx):
                    most_recent_extrema_idx = extrema_idx
                    extrema_price = price

            if extrema_price is not None:
                display_pivot_price = extrema_price
                # Update current_pivot to match the extrema_price for consistency
                self.current_pivot = extrema_price

        # Update the pivot price label text but don't display it (for functionality)
        formatted_pivot = f"{display_pivot_price:,.2f}"
        self.pivot_price_label.setText(f"Extrema: {formatted_pivot}")
        self.update_pivot_line(end_idx)
        if self.pivot_crossed:
            print("Pivot crossed detected - updating PML neurals and peak/trough rays")
            if self.show_peak_trough_rays.isChecked() and len(self.data) > 0:
                self.peak_trough_rays.find_peaks_and_troughs(self.data)
            self.pivot_crossed = False
        else:
            print("No pivot crossing detected - updating PML neurals")
        signals = {}

        # If classifier is trained, also add its predictions
        if not hasattr(self, '_skip_ml_calculations') or not self._skip_ml_calculations:
            if self.settings_dialog.enable_classifier.isChecked() and self.crossing_classifier.is_trained:
                for idx in self.crossing_points:
                    if idx <= end_idx and idx < len(self.data):
                        prediction_result = self.crossing_classifier.predict(self.data, idx, vector)
                        if prediction_result and prediction_result['confidence'] >= self.settings_dialog.confidence_threshold_spin.value():
                            # Determine if price is above or below vector
                            is_above = self.data['Close'].iloc[idx] > vector.iloc[idx]



        # Use the class attribute self.rebased_data for the candlestick item
        # Debug: Print the actual number of rows in rebased_data
        print(f"DEBUG: rebased_data contains {len(self.rebased_data)} rows starting from index {skip_candles}")
        print(f"DEBUG: Original data has {len(self.data)} rows, skipped first {skip_candles} rows, showing {len(self.rebased_data)} rows")

        # Get display options for visual elements
        display_options = self.settings_dialog.get_display_options()

        # Only create and add candlesticks if the setting is enabled
        if display_options.get('show_candlesticks', True):
            self.candlestick_item = CandlestickItem(
                self.rebased_data,
                bullish_color=self.chart_colors['bullish'],
                bearish_color=self.chart_colors['bearish'],
                signals={},  # Empty signals since pullback/reversal signals removed
                show_signals=False,  # Disable signals since pullback/reversal signals removed
                chart_colors=self.chart_colors,
                data_already_processed=False  # Let CandlestickItem handle skipping (same as candlestick chart)
            )
            self.plot_widget.addItem(self.candlestick_item)
        else:
            # Set candlestick_item to None when disabled to avoid reference errors
            self.candlestick_item = None

        # Always remove any existing legend first
        if hasattr(self, 'signals_legend') and self.signals_legend is not None:
            try:
                # Try different ways to remove the legend
                if hasattr(self.plot_widget.getPlotItem(), 'legend') and self.plot_widget.getPlotItem().legend is not None:
                    for sample_item in self.plot_widget.getPlotItem().legend.items:
                        try:
                            self.plot_widget.getPlotItem().legend.removeItem(sample_item[0])
                        except Exception as e:
                            print(f"Error removing legend item: {e}")
            except Exception as e:
                print(f"Error accessing legend: {e}")

            try:
                self.plot_widget.removeItem(self.signals_legend)
            except Exception as e:
                print(f"Error removing legend from plot: {e}")

            # Set to None to ensure garbage collection
            self.signals_legend = None

        # Clear any existing target level lines
        if hasattr(self, 'target_level_lines'):
            for line, label in self.target_level_lines:
                self.plot_widget.removeItem(line)
                self.plot_widget.removeItem(label)
            self.target_level_lines = []
        else:
            self.target_level_lines = []

        # Store the original vector values for reference
        if len(vector) > 0:
            self.vector_values = vector

        # Check if vector should be shown based on settings
        display_options = self.settings_dialog.get_display_options()
        if display_options.get('show_vector', True):
            try:
                # Import vector module - same approach as in candlestick_chart.py
                import vector as vector_module

                # Get vector length from parameter registry (this ensures it's synchronized with universal controls)
                from parameter_registry import default_registry
                wave_length = default_registry.get_value('vector_length')

                # Use the original OHLC data, not rebased data
                vector_values = vector_module.calculate_vector(self.data, length=wave_length, column='Close')

                # Store the original vector values for reference
                self.vector_values = vector_values

                # Prepare data for VectorItem using rebased data for display
                vector_data = []
                vector_values_list = []

                # Track the current cycle for vector rebasing
                current_cycle_start = 0
                # Initialize the pivot for the first cycle
                # Use the first vector value as the initial pivot to ensure proper alignment
                if len(vector_values) > skip_candles:  # Since rebased_data now starts from index skip_candles
                    current_cycle_pivot = vector_values.iloc[skip_candles]  # First vector value in rebased data
                    print(f"Initializing first cycle pivot to first vector value at index {skip_candles}: {current_cycle_pivot:.2f}")
                else:
                    current_cycle_pivot = self.visualization_pivot

                # Create data in the format expected by VectorItem (same as candlestick chart)
                # Use all rebased data with original indices (like candlestick chart)
                for i in range(len(self.rebased_data)):
                    # rebased_data now contains original indices, so use them directly
                    original_idx, open_price, high_price, low_price, close_price = self.rebased_data[i]

                    if original_idx <= end_idx:
                        # Format: (original_idx, open, high, low, close) - using original indices like candlestick chart
                        vector_data.append((original_idx, open_price, high_price, low_price, close_price))

                        # Check if this is a cycle change point (vector direction change)
                        is_cycle_change = False
                        if original_idx < len(cycles) and cycles[original_idx][1]:  # cycles[original_idx][1] is True if direction changed at this index
                            is_cycle_change = True
                            # Get the pivot price for this cycle
                            cycle_pivot = cycles[original_idx][2]  # This is the pivot price at the direction change
                            if cycle_pivot is not None:
                                # Start a new cycle with this pivot
                                current_cycle_start = i
                                current_cycle_pivot = cycle_pivot


                        # Rebase the vector value for display
                        if original_idx < len(vector_values):
                            # Special handling for the first candle in rebased data
                            if i == 0:
                                # For the first candle, ensure it starts at 0% to align with candlesticks
                                # The first candle should always be at the pivot (0% from pivot)
                                rebased_vector_value = 0.0
                                print(f"First candle: Setting rebased vector value to 0.0% to align with candlesticks")
                            # For cycle change points, calculate actual percentage instead of resetting to 0
                            elif is_cycle_change:
                                # Calculate the rebased vector value based on the current cycle's pivot
                                # This prevents the vector from resetting to 0% during rebasing
                                rebased_vector_value = ((vector_values.iloc[original_idx] / current_cycle_pivot) - 1) * 100
                                print(f"Cycle change at candle {i}: Setting rebased vector value to {rebased_vector_value:.2f}% (from pivot)")
                            else:
                                # Calculate the rebased vector value based on the current cycle's pivot
                                rebased_vector_value = ((vector_values.iloc[original_idx] / current_cycle_pivot) - 1) * 100

                            vector_values_list.append(rebased_vector_value)

                # Create vector item with transparent background (no coloring)
                self.vector_item = VectorItem(
                    data=vector_data,
                    vector_values=vector_values_list,
                    wave_length=wave_length,
                    line_color='purple',
                    line_width=2,
                    bg_up_color=(0, 0, 0, 0),  # Fully transparent (no green background)
                    bg_down_color=(0, 0, 0, 0),  # Fully transparent (no red background)
                    data_already_processed=False  # Let VectorItem handle skipping (same as candlestick chart)
                )
            except Exception as e:
                print(f"Error adding vector: {str(e)}")
                traceback.print_exc()

            # Add vector item to the plot
            self.plot_widget.addItem(self.vector_item)

            if len(vector) > 0:
                last_vector_price = vector.iloc[end_idx]
                # Calculate percentage from pivot
                last_vector_percentage = self.rebased_vector[-1] if self.rebased_vector else 0
                # Update vector price display with percentage information
                # Convert end_idx to display index (subtract skip_candles)
                display_end_idx = end_idx - skip_candles
                self.update_vector_price_box(display_end_idx, last_vector_percentage, last_vector_price)

            # Draw target levels if available
            if hasattr(self, 'target_levels') and self.target_levels:
                # Get the x-range of the visible chart
                x_min = timestamps[0]
                x_max = timestamps[-1]

                # Draw support levels
                for name, level in self.target_levels['support']:
                    # Convert level to rebased value
                    rebased_level = ((level / self.current_pivot) - 1) * 100

                    # Create horizontal line
                    line = pg.InfiniteLine(
                        pos=rebased_level,
                        angle=0,
                        pen=pg.mkPen(self.chart_colors['support'], width=1, style=QtCore.Qt.PenStyle.DashLine),
                        movable=False
                    )

                    # Create label
                    label = pg.TextItem(
                        text=f"{name} ({level:.2f})",
                        color=self.chart_colors['support'],
                        anchor=(0, 0.5)
                    )
                    label.setPos(x_max + 5, rebased_level)

                    # Add to plot
                    self.plot_widget.addItem(line)
                    self.plot_widget.addItem(label)

                    # Store for later removal
                    self.target_level_lines.append((line, label))

                # Draw resistance levels
                for name, level in self.target_levels['resistance']:
                    # Convert level to rebased value
                    rebased_level = ((level / self.current_pivot) - 1) * 100

                    # Create horizontal line
                    line = pg.InfiniteLine(
                        pos=rebased_level,
                        angle=0,
                        pen=pg.mkPen(self.chart_colors['resistance'], width=1, style=QtCore.Qt.PenStyle.DashLine),
                        movable=False
                    )

                    # Create label
                    label = pg.TextItem(
                        text=f"{name} ({level:.2f})",
                        color=self.chart_colors['resistance'],
                        anchor=(0, 0.5)
                    )
                    label.setPos(x_max + 5, rebased_level)

                    # Add to plot
                    self.plot_widget.addItem(line)
                    self.plot_widget.addItem(label)

                    # Store for later removal
                    self.target_level_lines.append((line, label))
        if confirmed_pivots:
            pivot_idx = confirmed_pivots[-1]

            # Use the vector price as pivot
            real_pivot_price = vector.iloc[pivot_idx]

            self.current_pivot = real_pivot_price
        else:
            real_pivot_price = self.current_pivot

        # Get the most recent extrema price for display (same logic as above)
        display_pivot_price = real_pivot_price
        if hasattr(self, 'extrema_prices') and self.extrema_prices:
            # Find the most recent extrema change before end_idx
            most_recent_extrema_idx = None
            extrema_price = None
            for extrema_idx, price in self.extrema_prices.items():
                if extrema_idx <= end_idx and (most_recent_extrema_idx is None or extrema_idx > most_recent_extrema_idx):
                    most_recent_extrema_idx = extrema_idx
                    extrema_price = price

            if extrema_price is not None:
                display_pivot_price = extrema_price
                # Update current_pivot to match the extrema_price for consistency
                self.current_pivot = extrema_price

        formatted_pivot = f"{display_pivot_price:,.2f}"
        self.pivot_price_label.setText(f"Extrema: {formatted_pivot}")
        self.plot_widget.getAxis('left').setTextPen('w')
        self.plot_widget.getAxis('bottom').setTextPen('w')
        self.plot_widget.getAxis('left').setLabel('Percentage from Extrema (%)')
        self.plot_widget.addItem(self.vLine, ignoreBounds=True)
        self.plot_widget.addItem(self.hLine, ignoreBounds=True)
        if view_range[0][0] is not None:
            self.plot_widget.setRange(xRange=view_range[0], yRange=view_range[1], padding=0)
        # Check if probability bands should be shown based on settings
        display_options = self.settings_dialog.get_display_options()
        if display_options.get('show_probability_bands', True):
            # Always draw threshold levels with update_labels=True to ensure labels are created
            self.draw_threshold_levels(self.rebased_data, update_labels=True)  # Use class attribute
        else:
            for line in self.threshold_lines:
                self.plot_widget.removeItem(line)
            self.threshold_lines = []
            for label in self.threshold_labels:
                self.plot_widget.removeItem(label)
            self.threshold_labels = []
        self.draw_cycle_rays(self.rebased_data)  # Use class attribute
        if hasattr(self, '_cached_pivot_transitions'):
            self.draw_imprints(self._cached_pivot_transitions)
        self.imprints_widget.setXRange(0, 1, padding=0)

        # Add density visualizations if enabled
        if display_options.get('show_density_heatmap', False) or display_options.get('show_density_profile', False):
            # Calculate density for the rebased data
            use_full_candle_length = display_options.get('use_full_candle_length', False)
            num_bins = display_options.get('density_bins', 50)
            bin_centers, density_counts, bin_edges = self._calculate_candle_density(
                self.rebased_data, use_full_candle_length, num_bins
            )

            if bin_centers is not None and density_counts is not None and bin_edges is not None:
                # Get the x-range for positioning
                x_range = (0, len(self.rebased_data))

                # Add heatmap if enabled
                if display_options.get('show_density_heatmap', False):
                    self._add_density_heatmap_to_chart(bin_centers, density_counts, bin_edges, x_range)

                # Add density profile if enabled
                if display_options.get('show_density_profile', False):
                    self._add_density_profile_to_chart(bin_centers, density_counts, bin_edges, x_range)



        # Draw the average cycle
        self.draw_white_lines()

        # Update the bottom candlestick chart
        self.update_candlestick_chart()

        # Restore original data
        self.data = original_data


    def update_pivot_line(self, idx):
        if self.pivot_line is not None:
            self.plot_widget.removeItem(self.pivot_line)

        # Get the most recent extrema price
        extrema_price = None
        if hasattr(self, 'extrema_prices') and self.extrema_prices:
            # Find the most recent extrema change before this candle
            most_recent_extrema_idx = None

            for extrema_idx, price in self.extrema_prices.items():
                if extrema_idx <= idx and (most_recent_extrema_idx is None or extrema_idx > most_recent_extrema_idx):
                    most_recent_extrema_idx = extrema_idx
                    extrema_price = price

        # Use extrema price if available, otherwise fall back to current_pivot
        pivot_to_display = extrema_price if extrema_price is not None else self.current_pivot

        if pivot_to_display is not None:
            self.pivot_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen('w', width=2))
            self.plot_widget.addItem(self.pivot_line)

            # Update the current_pivot to match the extrema_price to ensure consistency
            if extrema_price is not None:
                self.current_pivot = extrema_price

            # Update the pivot price label text but don't display it (for functionality)
            formatted_pivot = f"{self.current_pivot:,.2f}"
            self.pivot_price_label.setText(f"Extrema: {formatted_pivot}")




    def optimize_hyperparameters(self, length):
        """This method is no longer used for Donchian channel vector calculation

        It's kept for backward compatibility but doesn't do anything meaningful.

        Args:
            length: The vector length

        Returns:
            dict: Empty dictionary
        """
        # Initialize cache if it doesn't exist
        if not hasattr(self, '_optimized_params_cache'):
            self._optimized_params_cache = SimpleCache(50)

        # Check if we already have parameters for this length
        cached_params = self._optimized_params_cache.get(length)
        if cached_params is not None:
            return cached_params

        # Create empty params dictionary
        params = {}

        # Cache the parameters
        self._optimized_params_cache.put(length, params)

        return params

    def calculate_vector(self, data, length=20):
        """Calculate the vector using Donchian channel midpoint based on close prices with gap handling

        This is the main entry point for vector calculation. It uses a simple
        Donchian channel midpoint approach (also known as "The Line") based on close prices only.
        It also includes gap handling to adjust the vector when significant price gaps are detected.

        Args:
            data: The price data
            length: The lookback period for the Donchian channel

        Returns:
            pd.Series: The calculated vector
        """
        if len(data) < length:
            return pd.Series(index=data.index)

        # Import the vector module
        try:
            import vector
            print(f"Using vector.py implementation for vector calculation with length {length}")

            # Use the vector.py implementation with the real OHLC data
            # This is the same approach used in candlestick_chart.py
            result = vector.calculate_vector(data, length=length, column='Close')

            return result

        except ImportError as e:
            print(f"Error importing vector module: {str(e)}")
            print("Falling back to internal vector calculation")

            # Create cache key
            cache_key = (id(data), length)

            # Initialize cache if it doesn't exist
            if not hasattr(self, '_vector_cache'):
                self._vector_cache = SimpleCache(20)

            # Check if result is in cache
            cached_result = self._vector_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Calculate vector using the legacy method
            try:
                # Calculate the highest close and lowest close over the lookback period
                high = data['Close'].rolling(window=length, min_periods=length).max()
                low = data['Close'].rolling(window=length, min_periods=length).min()

                # Calculate the midpoint (The Line)
                result = (high + low) / 2

                # Fill NaN values at the beginning with the first valid value
                result = result.bfill()

                # Cache the result in the LRU cache
                self._vector_cache.put(cache_key, result)

                print(f"Vector calculation complete: {len(result)} values")
                return result

            except Exception as e:
                print(f"Error calculating Donchian channel vector: {str(e)}")
                # Return empty series in case of error
                return pd.Series(index=data.index)
    def enforce_pivot_constraint(self, row, vector_price):
        print(f"Enforcing pivot: Open={row['Open']}, Close={row['Close']}, High={row['High']}, Low={row['Low']}, Vector={vector_price}")

        # Test mode: Consider high/low prices in addition to open/close
        # This is an experimental feature for testing purposes
        use_high_low = hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'use_high_low_constraint') and \
                      self.settings_dialog.use_high_low_constraint.isChecked()

        if use_high_low:
            # Enhanced logic using High/Low prices
            if row['Close'] < row['Open']:  # Bearish candle
                # For bearish candles, check if the Low has penetrated the vector
                if row['Low'] > vector_price:
                    print("Pivot constraint FAILED (bearish): Even Low price didn't penetrate Vector")
                    return False
                # If Low penetrated but Close didn't, check penetration percentage
                elif row['Close'] > vector_price:
                    penetration_pct = (row['Low'] - vector_price) / vector_price * 100
                    if abs(penetration_pct) < 0.1:  # Less than 0.1% penetration
                        print(f"Pivot constraint FAILED (bearish): Insufficient Low penetration ({penetration_pct:.3f}%)")
                        return False
                    print(f"Pivot constraint PASSED (bearish): Sufficient Low penetration ({penetration_pct:.3f}%)")
            else:  # Bullish candle
                # For bullish candles, check if the High has penetrated the vector
                if row['High'] < vector_price:
                    print("Pivot constraint FAILED (bullish): Even High price didn't penetrate Vector")
                    return False
                # If High penetrated but Close didn't, check penetration percentage
                elif row['Close'] < vector_price:
                    penetration_pct = (row['High'] - vector_price) / vector_price * 100
                    if abs(penetration_pct) < 0.1:  # Less than 0.1% penetration
                        print(f"Pivot constraint FAILED (bullish): Insufficient High penetration ({penetration_pct:.3f}%)")
                        return False
                    print(f"Pivot constraint PASSED (bullish): Sufficient High penetration ({penetration_pct:.3f}%)")
        else:
            # Original logic using only Open/Close
            if row['Close'] < row['Open']:  # Bearish candle
                if row['Close'] > vector_price:
                    print("Pivot constraint FAILED (bearish): Close > Vector")
                    return False
            else:  # Bullish candle
                if row['Close'] < vector_price:
                    print("Pivot constraint FAILED (bullish): Close < Vector")
                    return False

        print("Pivot constraint passed")
        return True

    def check_vector_crossing(self, price, vector_price):
        """
        Revised signal-filtering crossing check function using the SignalProcessor.

        This function delegates most of the signal processing logic to the SignalProcessor class,
        which handles pivot constraints, RSI filtering, volatility-based adjustments, and
        signal strength calculation.
        """
        # Get classifier prediction if enabled
        prediction_result = None
        confidence_threshold = self.settings_dialog.confidence_threshold_spin.value()
        if not hasattr(self, '_skip_ml_calculations') or not self._skip_ml_calculations:
            if self.settings_dialog.enable_classifier.isChecked() and self.crossing_classifier.is_trained:
                if len(self.data) > self.settings_dialog.lookback_spin.value() and self.current_idx > self.settings_dialog.lookback_spin.value():
                    vector = self.calculate_vector(self.data, self.vector_length_spin.value())
                    prediction_result = self.crossing_classifier.predict(self.data, self.current_idx, vector)
                    if prediction_result and prediction_result['confidence'] < confidence_threshold:
                        prediction_result = None

        # Get RSI value if available
        rsi_value = None
        if hasattr(self, 'indicators') and 'RSI' in self.indicators and self.current_idx < len(self.indicators['RSI']):
            rsi_value = self.indicators['RSI'].iloc[self.current_idx]

        # Get volatility if available
        volatility = self.calculate_implied_volatility()

        # Process the potential crossing using the SignalProcessor
        result = self.signal_processor.process_potential_crossing(
            current_idx=self.current_idx,
            price=price,
            vector_price=vector_price,
            below_vector=self.below_vector,
            data=self.data,
            rsi_value=rsi_value,
            volatility=volatility,
            classifier_result=prediction_result
        )

        # Update the UI based on the result
        self.crossing_prediction_label.setText(result['message'])
        self.crossing_prediction_label.setStyleSheet(f"color: {result['color']};")

        # Update the enhanced signal display
        if hasattr(self, 'signal_display') and self.signal_display is not None:
            self.signal_display.update_signal(result)

        # Update the current_potential_crossing reference to match the signal processor's state
        self.current_potential_crossing = self.signal_processor.current_potential_crossing

        # Update confirmation period and magnitude threshold from signal processor
        # (these may have been adjusted based on volatility)
        self.confirmation_period = self.signal_processor.confirmation_period
        self.min_crossing_magnitude = self.signal_processor.min_crossing_magnitude

        # If confirmed, update the below_vector state and pivot
        if result['status'] == 'confirmed':
            self.below_vector = (result['direction'] == 'down')

            # Update pivot and set flag for rebasing
            old_pivot = self.current_pivot
            self.current_pivot = vector_price
            self.pivot_crossed = True

            # Store the extrema price (line price right before we transition above/below the extrema)
            if not hasattr(self, 'extrema_prices'):
                self.extrema_prices = {}
            self.extrema_prices[self.current_idx] = vector_price

            # Update UI - use the vector_price (which is the line price right before transition)
            formatted_pivot = f"{vector_price:,.2f}"
            self.pivot_price_label.setText(f"Extrema: {formatted_pivot}")

            # Make sure to update the visualization_pivot for proper rebasing
            self.visualization_pivot = vector_price

            # Log the pivot change
            print(f"Vector crossing confirmed: {result['direction']} - Pivot changed from {old_pivot:.2f} to {vector_price:.2f}")

            # Force a complete redraw with the new pivot
            print(f"Forcing complete redraw with new pivot: {vector_price:.2f}")
            self.plot_data_until(self.current_idx)

            return True

        # Return False for all other cases
        return False

    def update_vector_price_box(self, x, percentage, actual_price):
        if self.current_pivot is None:
            return

        # Calculate price from percentage if actual_price is not provided
        if actual_price is None:
            actual_price = self.current_pivot * (1 + percentage / 100.0)

        self.last_vector_price = actual_price

        # Add direction indicator (↑/↓) based on vector direction
        direction_indicator = ""
        if hasattr(self, 'vector_item') and self.vector_item is not None:
            # Get the current direction from the vector item
            current_direction = self.vector_item.get_current_direction()
            if current_direction == 'up':
                direction_indicator = " ↑"  # Up arrow for rising vector
            elif current_direction == 'down':
                direction_indicator = " ↓"  # Down arrow for falling vector

        # Add percentage information
        percentage_str = f" ({percentage:.2f}%)"

        # Get implied volatility
        iv = self.calculate_implied_volatility()

        # Format the display text but don't display it (for functionality)
        if iv:
            self.vector_price_label.setText(f"The Line: {actual_price:.2f}{direction_indicator}{percentage_str} | IV: {iv:.2f}%")
        else:
            self.vector_price_label.setText(f"The Line: {actual_price:.2f}{direction_indicator}{percentage_str}")

        # Update the line price label in the information panel if it's showing the current candle
        if hasattr(self, 'line_price_label') and hasattr(self, 'current_idx') and int(x) == self.current_idx:
            self.line_price_label.setText(f"Line Price: {actual_price:.2f} ({percentage:.2f}%)")
    def calculate_implied_volatility(self):
        """
        Calculate implied volatility using multiple methods.

        This method tries to get implied volatility from options data first,
        then falls back to historical volatility, and finally to ATR-based volatility.

        Returns:
            float: Volatility as a percentage, or None if not available
        """
        # First try to get implied volatility from options data
        if self.symbol_input.text().strip():
            try:
                symbol = self.symbol_input.text().strip().upper()
                ticker = yf.Ticker(symbol)
                try:
                    expirations = ticker.options
                    if expirations and len(expirations) > 0:
                        nearest_expiry = expirations[0]
                        opt = ticker.option_chain(nearest_expiry)
                        calls_iv = opt.calls['impliedVolatility'].mean() * 100
                        puts_iv = opt.puts['impliedVolatility'].mean() * 100
                        iv = (calls_iv + puts_iv) / 2
                        logger.info(f"Retrieved implied volatility for {symbol}: {iv:.2f}%")
                        return iv
                    else:
                        logger.info(f"No options data available for {symbol}")
                except Exception as e:
                    logger.warning(f"Error getting options data: {e}")
            except Exception as e:
                logger.warning(f"Error calculating implied volatility: {e}")

        # Then try historical volatility
        hist_vol = self.calculate_historical_volatility()
        if hist_vol is not None:
            return hist_vol

        # Finally, try ATR-based volatility
        atr_vol = self.calculate_atr_volatility()
        if atr_vol is not None:
            return atr_vol

        # If all methods fail, return None
        return None
    def calculate_historical_volatility(self):
        if self.data is None or len(self.data) < 5:
            return None
        try:
            log_returns = np.log(self.data['Close'] / self.data['Close'].shift(1)).dropna()
            if self.timeframe_combo.currentText() == '1d':
                trading_days = 252
            elif self.timeframe_combo.currentText() in ['1m', '2m', '5m', '15m', '30m', '60m']:
                minutes_map = {'1m': 1, '2m': 2, '5m': 5, '15m': 15, '30m': 30, '60m': 60}
                minutes = minutes_map.get(self.timeframe_combo.currentText(), 1)
                intervals_per_day = 6.5 * 60 / minutes
                trading_days = 252 * intervals_per_day
            else:
                trading_days = 252
            lookback = min(20, len(log_returns))
            recent_returns = log_returns.iloc[-lookback:]
            historical_volatility = recent_returns.std() * np.sqrt(trading_days) * 100
            print(f"Calculated historical volatility: {historical_volatility:.2f}%")
            return historical_volatility
        except Exception as e:
            print(f"Error calculating historical volatility: {e}")
            return None

    def calculate_atr_volatility(self):
        """
        Calculate volatility using ATR as a proxy.

        This method calculates the Average True Range (ATR) over a specified period
        and converts it to an annualized volatility percentage based on the current timeframe.

        Returns:
            float: Annualized volatility as a percentage, or None if not enough data
        """
        if not hasattr(self, 'data') or self.data is None or len(self.data) < 20:
            return None

        # Calculate ATR over the last 20 periods
        atr_period = 20
        if self.current_idx < atr_period:
            return None

        # Get the relevant data slice
        data_slice = self.data.iloc[max(0, self.current_idx - atr_period):self.current_idx + 1]

        # Calculate True Range
        high = data_slice['High'].values
        low = data_slice['Low'].values
        close = data_slice['Close'].values

        # Calculate True Range for each period
        tr = np.zeros(len(data_slice))
        for i in range(1, len(data_slice)):
            tr[i] = max(
                high[i] - low[i],  # Current high - low
                abs(high[i] - close[i-1]),  # Current high - previous close
                abs(low[i] - close[i-1])  # Current low - previous close
            )

        # Calculate ATR (Average True Range)
        atr = np.mean(tr[1:])  # Skip the first element as it's always 0

        # Convert ATR to annualized volatility (approximate)
        # For daily data, multiply by sqrt(252)
        # For hourly data, multiply by sqrt(252 * 6.5)
        # For minute data, adjust accordingly
        timeframe = self.timeframe_combo.currentText()
        if timeframe == '1d':
            multiplier = np.sqrt(252)
        elif timeframe == '1h' or timeframe == '60m':
            multiplier = np.sqrt(252 * 6.5)
        elif timeframe == '30m':
            multiplier = np.sqrt(252 * 6.5 * 2)
        elif timeframe == '15m':
            multiplier = np.sqrt(252 * 6.5 * 4)
        elif timeframe == '5m':
            multiplier = np.sqrt(252 * 6.5 * 12)
        elif timeframe == '1m':
            multiplier = np.sqrt(252 * 6.5 * 60)
        else:
            multiplier = np.sqrt(252)  # Default to daily

        # Calculate annualized volatility as a percentage
        avg_price = np.mean(close)
        annualized_volatility = (atr / avg_price) * 100 * multiplier

        # Log the volatility calculation
        logger.info(f"Calculated ATR volatility: {annualized_volatility:.2f}% (ATR: {atr:.4f}, Timeframe: {timeframe})")

        return annualized_volatility
    def adjust_settings_for_volatility(self, iv):
        """
        Adjust signal filtering settings based on current volatility.

        This method adjusts the confirmation period and minimum magnitude threshold
        based on the current market volatility. Higher volatility requires more
        confirmation and higher thresholds to filter out noise.

        Args:
            iv: Implied volatility as a percentage
        """
        if iv is None:
            return

        # Only adjust if the volatility-based filtering is enabled
        if not hasattr(self.settings_dialog, 'use_volatility_adjustment') or \
           not self.settings_dialog.use_volatility_adjustment.isChecked():
            return

        # Base values for medium volatility (around 20%)
        base_confirmation_period = 3
        base_min_magnitude = 0.05

        # Adjust confirmation period based on volatility
        # Higher volatility = more confirmation candles needed
        if iv < 10:  # Low volatility
            new_confirmation_period = max(1, base_confirmation_period - 1)
            new_min_magnitude = base_min_magnitude * 0.8  # Lower threshold for low volatility
        elif iv > 30:  # High volatility
            new_confirmation_period = min(5, base_confirmation_period + 1)
            new_min_magnitude = base_min_magnitude * 1.5  # Higher threshold for high volatility
        else:  # Medium volatility
            new_confirmation_period = base_confirmation_period
            new_min_magnitude = base_min_magnitude

        # Update the settings in both the main app and the signal processor
        self.confirmation_period = new_confirmation_period
        self.min_crossing_magnitude = new_min_magnitude

        # Update signal processor if available
        if hasattr(self, 'signal_processor'):
            self.signal_processor.confirmation_period = new_confirmation_period
            self.signal_processor.min_crossing_magnitude = new_min_magnitude
            logger.info(f"Updated signal processor settings for volatility {iv:.1f}%")

        # Update the UI to reflect these changes
        if hasattr(self, 'crossing_prediction_label'):
            self.crossing_prediction_label.setText(
                f"Volatility: {iv:.1f}% → Confirmation: {new_confirmation_period}, "
                f"Min Magnitude: {new_min_magnitude:.2f}%"
            )

        logger.info(f"Adjusted signal filtering for volatility {iv:.1f}%: "
                   f"Confirmation={new_confirmation_period}, Magnitude={new_min_magnitude:.2f}%")
    def toggle_auto_fetch(self, state):
        """Toggle auto-fetch data feature"""
        if state:
            # Start the update timer and countdown timer
            self.update_timer.start(60000)  # 60 seconds
            self.countdown_seconds = 60
            self.countdown_timer.start(1000)  # Update countdown every second
            self.countdown_label.setText(f"{self.countdown_seconds}s")
            print("Auto-fetch enabled: Data will refresh every 60 seconds")
        else:
            # Stop the timers
            self.update_timer.stop()
            self.countdown_timer.stop()
            self.countdown_label.setText("60s")
            print("Auto-fetch disabled")

        # Update the auto_fetch_check in the settings dialog
        if hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'auto_fetch_check'):
            if self.settings_dialog.auto_fetch_check.isChecked() != state:
                self.settings_dialog.auto_fetch_check.setChecked(state)

    def update_countdown(self):
        """Update the countdown timer display"""
        self.countdown_seconds -= 1
        if self.countdown_seconds <= 0:
            self.countdown_seconds = 60
        self.countdown_label.setText(f"{self.countdown_seconds}s")

        # Change color when getting close to refresh
        if self.countdown_seconds <= 5:
            self.countdown_label.setStyleSheet("color: #F44336; font-weight: bold;")  # Red color
        else:
            self.countdown_label.setStyleSheet("color: #FFC107; font-weight: bold;")  # Amber color

    def update_data(self):
        # Reset countdown timer
        self.countdown_seconds = 60
        self.countdown_label.setText(f"{self.countdown_seconds}s")
        self.countdown_label.setStyleSheet("color: #FFC107; font-weight: bold;")  # Amber color

        # Start timer to measure fetch time
        fetch_start_time = datetime.now()

        # Temporarily disable ML functionality
        self.disable_ml_functionality()

        if not self.symbol_input.text().strip():
            # Restore ML functionality
            self.restore_ml_functionality()
            return
        symbol = self.symbol_input.text().strip().upper()
        try:
            # Use data dispatcher for live updates with proper async handling
            from data_dispatcher import DataDispatcher
            from parameter_registry import default_registry

            dispatcher = DataDispatcher.get_instance()

            # Get timeframe from universal controls
            timeframe = default_registry.get_value('timeframe')

            # Set up proper async data handler for live updates
            def on_update_data_ready(symbol_received, data):
                if symbol_received == symbol:
                    try:
                        new_data = data.copy()

                        # Log which data source was used for live updates
                        data_source = new_data.attrs.get('data_source', 'unknown')
                        print(f"Market Odds Live Update: Fetched data from {data_source}")

                        # Process the live update data
                        self._process_live_update(new_data, fetch_start_time)

                    except Exception as e:
                        print(f"Error processing live update: {e}")
                        self.restore_ml_functionality()

            def on_update_error(error_message):
                print(f"Live update error: {error_message}")
                self.restore_ml_functionality()

            # Connect handlers
            self._update_data_ready_connection = dispatcher.data_fetched.connect(on_update_data_ready)
            self._update_error_connection = dispatcher.error.connect(on_update_error)

            # Fetch 1 day of data for live updates
            dispatcher.fetch_data(symbol, timeframe, 1)

            # Return early - processing will continue in on_update_data_ready
            return
        except Exception as e:
            print(f"Error updating data: {str(e)}")
            self.update_timer.stop()
            if hasattr(self, 'auto_fetch'):
                self.auto_fetch.setChecked(False)

            # Restore ML functionality even in case of error
            self.restore_ml_functionality()
    def fetch_data(self):
        symbol = self.symbol_input.text().strip().upper()
        if not symbol:
            from dialog_manager import warning
            warning(self, "Error", "Please enter a symbol")
            self.update_timer.stop()
            self.iv_update_timer.stop()
            return
        try:
            # Show the internal loading screen
            self.loading_screen.set_message(f"Loading data for {symbol}...")
            self.loading_screen.show()

            # Preset functionality has been removed

            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

            # Temporarily disable ML functionality
            self.disable_ml_functionality()

            if hasattr(self, 'crossing_classifier'):
                print("Fetching data with classifier training disabled")

            try:
                # Update loading message
                self.loading_screen.set_message(f"Fetching {self.timeframe_combo.currentText()} data for {symbol}...")

                # Try async data dispatcher first, with synchronous fallback
                from data_dispatcher import DataDispatcher, DataFetchThread
                from parameter_registry import default_registry

                # Get parameters from universal controls via registry
                days_to_load = default_registry.get_value('days_to_load')
                interval = default_registry.get_value('timeframe')

                # Update local controls to match universal controls
                self.dtl_spin.setValue(days_to_load)
                self.timeframe_combo.setCurrentText(interval)
                self.update_dtl_max_value(interval)

                # Try synchronous data fetching as fallback for broken async system
                print(f"Fetching data for {symbol} using synchronous fallback...")
                print(f"Requesting {days_to_load} days")

                try:
                    # Create a data fetch thread but run it synchronously
                    dispatcher = DataDispatcher.get_instance()
                    data_source = dispatcher.get_data_source()

                    # Create thread instance to use its methods
                    fetch_thread = DataFetchThread(symbol, interval, days_to_load, data_source)

                    # Determine which data source to use
                    use_schwab = fetch_thread._should_use_schwab(interval, days_to_load)

                    if use_schwab:
                        print(f"Using Schwab API for {symbol}")
                        self.update_fetch_status(f"Fetching {symbol} from Schwab API...", "#ffaa00")
                        self.data = fetch_thread._fetch_schwab_data()
                        data_source_used = 'schwab'
                    else:
                        print(f"Using Yahoo Finance for {symbol}")
                        self.update_fetch_status(f"Fetching {symbol} from Yahoo Finance...", "#ffaa00")
                        self.data = self._fetch_yfinance_with_extended_hours(symbol, interval, days_to_load)
                        data_source_used = 'yfinance'

                    if self.data is None or self.data.empty:
                        raise Exception(f"No data returned for symbol {symbol}")

                    # Remove timezone info if present
                    if self.data.index.tz is not None:
                        self.data.index = self.data.index.tz_localize(None)

                    # Store the symbol and data source in the DataFrame attributes
                    self.data.attrs['symbol'] = symbol
                    self.data.attrs['data_source'] = data_source_used

                    print(f"Market Odds: Successfully fetched {len(self.data)} rows from {data_source_used}")
                    self.update_fetch_status(f"Data fetched for {symbol} ({len(self.data)} rows)", "#55ff55")

                    # Continue with data processing
                    self._process_fetched_data()
                    return

                except Exception as sync_error:
                    print(f"Synchronous fetch failed: {sync_error}")
                    self.update_fetch_status(f"Error fetching {symbol}", "#ff5555")
                    from dialog_manager import warning
                    warning(self, "Error", f"Error fetching data: {str(sync_error)}")
                    self._cleanup_after_fetch()
                    return

            except Exception as e:
                from dialog_manager import warning
                warning(self, "Error", f"Error fetching data: {str(e)}")
                QtWidgets.QApplication.restoreOverrideCursor()
                self.update_timer.stop()
                self.loading_screen.hide()
                return

        except Exception as e:
            from dialog_manager import critical
            critical(self, "Error", f"Error in fetch_data: {str(e)}")
            QtWidgets.QApplication.restoreOverrideCursor()
            self.update_timer.stop()
            self.loading_screen.hide()
            # Restore ML functionality even in case of error
            self.restore_ml_functionality()

    def _process_fetched_data(self):
        """Process the fetched data after it arrives from the data dispatcher"""
        try:
            # Disconnect the signal handlers
            if hasattr(self, '_data_ready_connection'):
                self._data_ready_connection.disconnect()
            if hasattr(self, '_error_connection'):
                self._error_connection.disconnect()

            # Get symbol from the symbol input
            symbol = self.symbol_input.text().strip().upper()

            # Get parameters from universal controls
            from parameter_registry import default_registry
            interval = default_registry.get_value('timeframe')
            days_to_load = default_registry.get_value('days_to_load')

            if len(self.data) > 20000:
                print(f"Warning: Limiting data from {len(self.data)} to 20000 points")
                self.data = self.data.tail(20000)

            if self.data.empty:
                QtWidgets.QMessageBox.warning(self, "Error", "No data returned for symbol")
                QtWidgets.QApplication.restoreOverrideCursor()
                self.update_timer.stop()
                # Hide the loading screen
                self.loading_screen.hide()
                return
            if self.data.index.tz is not None:
                self.data.index = self.data.index.tz_localize(None)
            if self.data.isnull().any().any():
                print("Warning: Data contains NaN values, cleaning...")
                self.data = self.data.fillna(method='ffill').fillna(method='bfill')
            required_columns = ['Open', 'High', 'Low', 'Close']
            for col in required_columns:
                if col not in self.data.columns:
                    QtWidgets.QMessageBox.warning(self, "Error", f"Data missing {col} column")
                    QtWidgets.QApplication.restoreOverrideCursor()
                    self.update_timer.stop()
                    return
            self.current_pivot = None
            self.last_vector_price = None
            self.below_vector = False
            self.crossing_points = []
            self.extrema_prices = {}  # Reset extrema prices for new data
            self.crossing_classifier.is_trained = False
            self.crossing_prediction_label.setText("Crossing: --")
            self.crossing_prediction_label.setStyleSheet("color: yellow;")
            self.current_idx = 0
            chunk_size = min(500, len(self.data))
            self.plot_data_until(self.current_idx)
            print(f"Loaded {len(self.data)} data points (requested {days_to_load})")
            print(f"Calculating vector for {len(self.data)} data points...")

            # Update loading message
            self.loading_screen.set_message(f"Calculating vector for {symbol}...")

            vector = self.calculate_vector(self.data, self.vector_length_spin.value())
            if len(vector) > 0 and len(self.data) > 0:
                self.current_pivot = vector.iloc[0]

                # Update loading message
                self.loading_screen.set_message(f"Processing data for {symbol}...")

                for i in range(0, len(self.data), chunk_size):
                    end_idx = min(i + chunk_size, len(self.data))
                    self._process_data_chunk(vector, 0, end_idx)
                # Classifier training disabled
                if len(self.crossing_points) >= 10:
                    self.crossing_prediction_label.setText("Ready to train classifier (manual)")
                else:
                    self.crossing_prediction_label.setText("Not enough data for training")
                self.current_idx = len(self.data) - 1
                self.plot_data_until(self.current_idx)
            # If auto-fetch is enabled, start the countdown timer
            if hasattr(self, 'auto_fetch') and self.auto_fetch.isChecked():
                self.update_timer.start(60000)  # 60 seconds
                self.countdown_seconds = 60
                self.countdown_timer.start(1000)  # Update countdown every second
                self.countdown_label.setText(f"{self.countdown_seconds}s")
                print("Auto-fetch enabled: Data will refresh every 60 seconds")
            else:
                self.update_timer.stop()
                self.countdown_timer.stop()

            self.iv_update_timer.stop()
            # Update loading message
            self.loading_screen.set_message(f"Finalizing chart for {symbol}...")

            QtWidgets.QApplication.restoreOverrideCursor()

            # Hide the loading screen
            self.loading_screen.hide()

            # Online and adaptive learning have been removed

            # Emit the data_fetched signal to notify other components
            # Use the original days_to_load (not actual_days_to_load) for the signal
            # since other components expect the user-requested amount
            self.data_fetched.emit(symbol, interval, days_to_load)

            # Restore ML functionality
            self.restore_ml_functionality()
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Error fetching data: {str(e)}")
            QtWidgets.QApplication.restoreOverrideCursor()
            # Hide the loading screen in case of error
            self.loading_screen.hide()
            self.update_timer.stop()

            # Restore ML functionality even in case of error
            self.restore_ml_functionality()

    def _process_live_update(self, new_data, fetch_start_time):
        """Process live update data from the data dispatcher"""
        try:
            # Disconnect the signal handlers
            if hasattr(self, '_update_data_ready_connection'):
                self._update_data_ready_connection.disconnect()
            if hasattr(self, '_update_error_connection'):
                self._update_error_connection.disconnect()

            if new_data.empty:
                return

            if new_data.index.tz is not None:
                new_data.index = new_data.index.tz_localize(None)

            if new_data.isnull().any().any():
                new_data = new_data.fillna(method='ffill').fillna(method='bfill')

            if len(new_data) > 0 and (self.data is None or new_data.index[-1] > self.data.index[-1]):
                if self.data is not None:
                    if len(self.data) > 5000:
                        print(f"Limiting historical data from {len(self.data)} to 5000 points")
                        self.data = self.data.iloc[-5000:]
                    combined = pd.concat([self.data, new_data])
                    self.data = combined[~combined.index.duplicated(keep='last')]
                else:
                    self.data = new_data

                self.current_idx = len(self.data) - 1
                self.plot_data_until(self.current_idx)

                if self.current_idx > 0:
                    vector = self.calculate_vector(self.data, self.vector_length_spin.value())
                    if len(vector) > self.current_idx:
                        latest_price = self.data['Close'].iloc[-1]
                        latest_vector = vector.iloc[-1]

                        # Check for vector crossing and force UI update
                        crossing_detected = self.check_vector_crossing(latest_price, latest_vector)

                        # If a crossing was detected, make sure to update the UI
                        if crossing_detected:
                            print("Reversal signal detected during live update - updating UI")
                            # Force a redraw to show the signal
                            self.plot_data_until(self.current_idx)
                        else:
                            # Even if no crossing was detected, update the signal display if it exists
                            if hasattr(self, 'signal_display') and self.signal_display is not None:
                                self.signal_display.update()

            # Calculate and display fetch time
            fetch_time = (datetime.now() - fetch_start_time).total_seconds()
            print(f"Live update completed in {fetch_time:.2f} seconds")

            # Restore ML functionality
            self.restore_ml_functionality()

        except Exception as e:
            print(f"Error processing live update: {e}")
            self.restore_ml_functionality()

    def _cleanup_after_fetch(self):
        """Clean up after a failed data fetch"""
        try:
            # Disconnect the signal handlers
            if hasattr(self, '_data_ready_connection'):
                self._data_ready_connection.disconnect()
            if hasattr(self, '_error_connection'):
                self._error_connection.disconnect()

            # Restore UI state
            QtWidgets.QApplication.restoreOverrideCursor()
            self.update_timer.stop()
            self.loading_screen.hide()

        except Exception as e:
            print(f"Error during cleanup: {e}")

    def _process_data_chunk(self, vector, start_idx, end_idx):
        if start_idx >= len(self.data) or start_idx >= len(vector) or start_idx >= end_idx:
            return
        prices = self.data['Close'].iloc[start_idx:end_idx].values
        vector_prices = vector.iloc[start_idx:end_idx].values
        temp_below_vector = False if start_idx == 0 else self.below_vector
        price_below_vector = prices < vector_prices
        if start_idx > 0:
            if price_below_vector[0] != temp_below_vector:
                self.crossing_points.append(start_idx)
                temp_below_vector = price_below_vector[0]
        crossings = np.where(price_below_vector[1:] != price_below_vector[:-1])[0]
        for cross_idx in crossings:
            self.crossing_points.append(start_idx + cross_idx + 1)
            temp_below_vector = not temp_below_vector
        if end_idx >= len(self.data):
            self.below_vector = temp_below_vector
    def _process_data_sequentially(self, vector):
        chunk_size = min(1000, len(self.data))
        if not hasattr(self, 'crossing_points') or self.crossing_points is None:
            self.crossing_points = []
        for i in range(0, len(self.data), chunk_size):
            end_idx = min(i + chunk_size, len(self.data))
            self._process_data_chunk(vector, i, end_idx)
    def show_calibration_curve(self):
        """Display the calibration curve in a separate window."""
        # Check if the classifier is trained
        if not hasattr(self.crossing_classifier, 'is_trained') or not self.crossing_classifier.is_trained:
            QtWidgets.QMessageBox.information(self, "Calibration", "The classifier is not trained. Please train the model first.")
            return

        # Check if calibration metrics exist
        if not hasattr(self.crossing_classifier, 'calibration_metrics') or not self.crossing_classifier.calibration_metrics:
            # Enable calibration and retrain
            if QtWidgets.QMessageBox.question(self, "Calibration",
                "No calibration data available. Would you like to enable calibration and retrain the model?",
                QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No) == QtWidgets.QMessageBox.StandardButton.Yes:

                # Enable calibration in settings
                if hasattr(self.settings_dialog, 'enable_calibration'):
                    self.settings_dialog.enable_calibration.setChecked(True)
                    self.crossing_classifier.use_calibrated_probabilities = True

                    # Retrain the model
                    self.train_classifier()

                    # Check if calibration was successful
                    if hasattr(self.crossing_classifier, 'calibration_metrics') and self.crossing_classifier.calibration_metrics:
                        # Continue to show the calibration curve
                        pass
                    else:
                        QtWidgets.QMessageBox.information(self, "Calibration",
                            "Calibration failed. This could be due to insufficient training data or an error during calibration.\n\n"
                            "Try the following:\n"
                            "1. Make sure you have enough training data (at least 20 samples)\n"
                            "2. Make sure your training data has both classes (pullback and reversal)")
                        return
                else:
                    QtWidgets.QMessageBox.information(self, "Calibration", "Could not find calibration settings.")
                    return
            else:
                return

        # Check if the calibration metrics are in the correct format
        if 'uncalibrated' not in self.crossing_classifier.calibration_metrics or 'calibrated' not in self.crossing_classifier.calibration_metrics:
            QtWidgets.QMessageBox.information(self, "Calibration",
                "Calibration data is not in the correct format. Please retrain the model.")
            return

        try:
            # Create a figure with the calibration curve
            fig = self.crossing_classifier.plot_calibration_curve()
            if fig is None:
                QtWidgets.QMessageBox.warning(self, "Error", "Could not generate calibration curve.")
                return

            # Create a dialog to display the figure
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle("Probability Calibration Curve")
            dialog.resize(800, 600)

            # Create a layout for the dialog
            layout = QtWidgets.QVBoxLayout(dialog)

            # Create a matplotlib widget
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            canvas = FigureCanvas(fig)
            layout.addWidget(canvas)

            # Add a label explaining the calibration curve
            explanation = QtWidgets.QLabel(
                "<p>The calibration curve shows how well the model's predicted probabilities "
                "match the actual frequencies of events.</p>"
                "<p>A perfectly calibrated model follows the diagonal line. Points above the line "
                "indicate underconfidence, while points below indicate overconfidence.</p>"
                "<p>The Brier score is a measure of calibration quality (lower is better).</p>"
            )
            explanation.setWordWrap(True)
            layout.addWidget(explanation)

            # Add a close button
            button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.StandardButton.Close)
            button_box.rejected.connect(dialog.reject)
            layout.addWidget(button_box)

            # Show the dialog
            dialog.exec()

        except Exception as e:
            print(f"Error showing calibration curve: {str(e)}")
            traceback.print_exc()
            QtWidgets.QMessageBox.warning(self, "Error", f"Could not display calibration curve: {str(e)}")

    def train_classifier(self):
        if not hasattr(self, 'data') or self.data is None or len(self.data) == 0:
            QtWidgets.QMessageBox.warning(self, "Error", "No data available for training")
            return

        if not hasattr(self, 'crossing_points') or len(self.crossing_points) < 10:
            QtWidgets.QMessageBox.warning(self, "Error", "Not enough crossing points for training")
            return

        try:
            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)
            vector = self.calculate_vector(self.data, self.vector_length_spin.value())

            print("Training classifier manually")

            self.crossing_prediction_label.setText("Training classifier...")

            if self.crossing_classifier.train(self.data, vector, self.crossing_points):
                self.crossing_prediction_label.setText("Classifier trained successfully")
                self.crossing_prediction_label.setStyleSheet("color: green;")

                # Performance metrics removed

                # Get optimized parameters for current vector length
                vector_length = self.vector_length_spin.value()
                optimized_params = self.optimize_hyperparameters(vector_length)

                # Format the optimized parameters for display
                params_text = (
                    f"Classifier trained successfully with optimized settings:\n"
                    f"Vector Length: {vector_length}\n"
                    f"Model Type: {self.crossing_classifier.metrics.get('model_type', 'Unknown')}\n\n"
                )

                # Add XGBoost parameters if they exist in optimized_params
                if all(k in optimized_params for k in ['xgb_n_estimators', 'xgb_learning_rate', 'xgb_max_depth']):
                    params_text += (f"\nXGBoost Parameters:\n"
                                   f"  n_estimators: {optimized_params['xgb_n_estimators']}\n"
                                   f"  learning_rate: {optimized_params['xgb_learning_rate']}\n"
                                   f"  max_depth: {optimized_params['xgb_max_depth']}\n"
                                   f"  gamma: {optimized_params.get('xgb_gamma', 0)}\n"
                                   f"  subsample: {optimized_params.get('xgb_subsample', 0.8)}\n"
                                  )

                # Add LightGBM parameters if they exist in optimized_params
                if all(k in optimized_params for k in ['lgb_n_estimators', 'lgb_learning_rate', 'lgb_max_depth']):
                    params_text += (f"\nLightGBM Parameters:\n"
                                   f"  n_estimators: {optimized_params['lgb_n_estimators']}\n"
                                   f"  learning_rate: {optimized_params['lgb_learning_rate']}\n"
                                   f"  max_depth: {optimized_params['lgb_max_depth']}\n"
                                   f"  num_leaves: {optimized_params.get('lgb_num_leaves', 31)}\n"
                                   f"  subsample: {optimized_params.get('lgb_subsample', 0.8)}\n"
                                  )

                self.crossing_prediction_label.setText("Classifier trained successfully")
                self.crossing_prediction_label.setStyleSheet("color: green;")

                # ML actions removed

                # Show the optimized parameters in a message box
                QtWidgets.QMessageBox.information(self, "Training Complete", params_text)

                # Performance metrics removed
            else:
                self.crossing_prediction_label.setText("Training failed")
                self.crossing_prediction_label.setStyleSheet("color: red;")

            QtWidgets.QApplication.restoreOverrideCursor()
        except Exception as e:
            QtWidgets.QApplication.restoreOverrideCursor()
            self.crossing_prediction_label.setText("Training error")
            self.crossing_prediction_label.setStyleSheet("color: red;")
            QtWidgets.QMessageBox.warning(self, "Error", f"Error training classifier: {str(e)}")

    # Preset methods have been removed

    def vector_length_changed(self, skip_ml_calculations=False):
        """Handle changes to the vector length spinner

        Args:
            skip_ml_calculations: If True, skip ML-related calculations (used when called from settings dialog)
        """
        if not hasattr(self, 'data') or self.data is None:
            return

        try:
            if self.data is not None:
                view_range = self.plot_widget.viewRange()
                print("Recalculating vector with new parameters...")
                vector_length = self.vector_length_spin.value()

                # Set flag to skip ML calculations in plot_data_until
                old_skip_ml_value = getattr(self, '_skip_ml_calculations', False)
                self._skip_ml_calculations = skip_ml_calculations

                try:
                    # Recalculate the vector
                    vector = self.calculate_vector(self.data, vector_length)

                    # Reset crossing points
                    self.crossing_points = []

                    # Process the data with the new vector
                    self._process_data_sequentially(vector)

                    # Update the plot
                    self.plot_data_until(len(self.data) - 1)

                    # Restore the view range
                    self.plot_widget.setXRange(view_range[0][0], view_range[0][1])
                    self.plot_widget.setYRange(view_range[1][0], view_range[1][1])

                    # Skip ML-related calculations if requested (when called from settings dialog)
                    if not skip_ml_calculations:
                        # Reset the classifier training status
                        self.crossing_classifier.is_trained = False
                        self.crossing_prediction_label.setText("Vector changed - retraining needed")
                        self.crossing_prediction_label.setStyleSheet("color: yellow;")

                        # Classifier training disabled
                        if len(self.crossing_points) >= 10:
                            pass  # Training action removed
                        else:
                            self.crossing_prediction_label.setText("Not enough data for training")
                    else:
                        print("Skipping ML calculations as requested")
                finally:
                    # Restore the original skip_ml_calculations value
                    self._skip_ml_calculations = old_skip_ml_value
        except Exception as e:
            print(f"Error updating vector: {str(e)}")
            traceback.print_exc()





    def evaluate_prediction_accuracy(self, data, vector, crossings):
        """Evaluate the accuracy of pullback and reversal predictions

        This function evaluates how well the current vector settings predict
        pullbacks and reversals by analyzing past crossing points.

        Args:
            data: The price data
            vector: The calculated vector
            crossings: List of crossing points

        Returns:
            float: Accuracy score (0.0 to 1.0)
        """
        if not hasattr(self, 'crossing_classifier') or not self.crossing_classifier.is_trained or len(crossings) < 10:
            return 0.0

        try:
            # Use the crossing classifier to evaluate accuracy
            correct_predictions = 0
            total_predictions = 0

            for idx in crossings:
                if idx < self.settings_dialog.lookback_spin.value() or idx >= len(data) - self.settings_dialog.lookahead_spin.value():
                    continue

                # Get the actual outcome (pullback or reversal)
                was_above = data['Close'].iloc[idx-1] > vector.iloc[idx-1]
                lookahead = self.settings_dialog.lookahead_spin.value()
                future_data = data.iloc[idx:idx+lookahead]

                if len(future_data) < lookahead:
                    continue

                max_move = 0
                returned_to_original_side = False

                for _, row in future_data.iterrows():
                    if was_above:
                        # Started above vector, looking for max downward move
                        move_pct = (vector.iloc[idx] / row['Low'] - 1) * 100
                        if row['Close'] > vector.iloc[idx]:
                            returned_to_original_side = True
                    else:
                        # Started below vector, looking for max upward move
                        move_pct = (row['High'] / vector.iloc[idx] - 1) * 100
                        if row['Close'] < vector.iloc[idx]:
                            returned_to_original_side = True

                    max_move = max(max_move, move_pct)

                # Determine actual outcome
                actual_outcome = 'Pullback' if returned_to_original_side else 'Reversal'

                # Get prediction
                prediction_result = self.crossing_classifier.predict(data, idx, vector)
                if prediction_result and prediction_result['confidence'] >= self.settings_dialog.confidence_threshold_spin.value():
                    predicted_outcome = prediction_result['prediction']
                    if predicted_outcome == actual_outcome:
                        correct_predictions += 1
                    total_predictions += 1

            if total_predictions > 0:
                return correct_predictions / total_predictions
            return 0.0
        except Exception as e:
            print(f"Error evaluating prediction accuracy: {str(e)}")
            return 0.0

    def set_drawing_tool(self, tool_type):
        """Set the current drawing tool and update button states"""
        # Reset all button styles
        buttons = [self.mouse_button, self.line_button, self.vertical_line_button,
                  self.horizontal_line_button, self.rectangle_button, self.text_button]

        button_style = """
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }
        """

        active_button_style = """
            QPushButton {
                background-color: #3d3d3d;
                color: white;
                border: 1px solid #007acc;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4d4d4d;
                border: 1px solid #0098ff;
            }
            QPushButton:pressed {
                background-color: #2d2d2d;
            }
        """

        # Reset all buttons to default style
        for button in buttons:
            button.setStyleSheet(button_style)

        # If tool_type is None, we're deactivating all tools (mouse mode)
        if tool_type is None:
            self.current_drawing_tool = None
            self.mouse_button.setStyleSheet(active_button_style)
            return

        # Set the active tool and highlight the corresponding button
        self.current_drawing_tool = tool_type

        # Highlight the active button
        if tool_type == 'line':
            self.line_button.setStyleSheet(active_button_style)
        elif tool_type == 'vertical':
            self.vertical_line_button.setStyleSheet(active_button_style)
        elif tool_type == 'horizontal':
            self.horizontal_line_button.setStyleSheet(active_button_style)
        elif tool_type == 'rectangle':
            self.rectangle_button.setStyleSheet(active_button_style)
        elif tool_type == 'text':
            self.text_button.setStyleSheet(active_button_style)
    def mouse_clicked(self, event):
        # Only process left mouse button clicks when a drawing tool is active
        if event.buttons() & QtCore.Qt.MouseButton.LeftButton == 0 or not self.current_drawing_tool:
            return

        # Get the position in plot coordinates
        pos = self.plot_widget.plotItem.vb.mapSceneToView(event.scenePos())

        # Handle drawing based on the current state and tool
        if not self.drawing_tools.drawing:
            # Starting a new drawing
            self.drawing_tools.start_drawing(pos, self.current_drawing_tool)

            # Special handling for text tool (immediate completion)
            if self.current_drawing_tool == 'text':
                text, ok = QtWidgets.QInputDialog.getText(self, 'Add Text', 'Enter text:')
                if ok and text:
                    self.drawing_tools.add_text(pos, text)
                    self.set_drawing_tool(None)  # Reset tool after adding text
                else:
                    # Cancel drawing if user cancels text entry
                    self.drawing_tools.drawing = False
                    if self.drawing_tools.current_item:
                        self.plot_widget.removeItem(self.drawing_tools.current_item)
                    self.drawing_tools.current_item = None
                    self.set_drawing_tool(None)

            # Special handling for horizontal and vertical lines (immediate completion)
            elif self.current_drawing_tool in ['horizontal', 'vertical']:
                self.drawing_tools.finish_drawing(pos)
                self.set_drawing_tool(None)  # Reset tool after drawing

            # For line and rectangle, we need a second click to finish
            # So we don't do anything else here
        else:
            # We're already drawing, so this click finishes the drawing
            self.drawing_tools.finish_drawing(pos)
            self.set_drawing_tool(None)  # Reset tool after drawing
    def mouse_moved(self, event):
        """Handle mouse movement for drawing tools and crosshair"""
        # Convert event position to plot coordinates
        if isinstance(event, QtCore.QPointF):
            # Direct QPointF from SignalProxy
            pos = self.plot_widget.plotItem.vb.mapSceneToView(event)
        else:
            # List from SignalProxy
            pos = self.plot_widget.plotItem.vb.mapSceneToView(event[0])

        # Update drawing if in progress
        if self.drawing_tools.drawing:
            # For text tool, just update the position
            if self.current_drawing_tool == 'text' and self.drawing_tools.current_item:
                self.drawing_tools.current_item.setPos(pos.x(), pos.y())
            # For other tools, use the update_drawing method
            elif self.current_drawing_tool in ['line', 'rectangle', 'horizontal', 'vertical']:
                self.drawing_tools.update_drawing(pos)

            # Show a different cursor when drawing
            self.plot_widget.setCursor(QtCore.Qt.CursorShape.CrossCursor)
    def clear_all_drawings(self):
        self.drawing_tools.clear_all()
        self.set_drawing_tool(None)
    def handle_right_click(self, event):
        if event.buttons() & QtCore.Qt.MouseButton.RightButton == 0:
            return
        pos = self.plot_widget.plotItem.vb.mapSceneToView(event.scenePos())
        if self.drawing_tools.select_item(pos):
            item = self.drawing_tools.selected_item
            menu = QtWidgets.QMenu(self)
            color_menu = menu.addMenu("Change Color")
            for color_name, color_code in self.drawing_tools.colors.items():
                action = menu.addAction(color_name)
                action.triggered.connect(lambda checked, c=color_code:
                                           self.drawing_tools.change_item_color(item, c))
            add_text_action = menu.addAction("Add/Edit Label")
            add_text_action.triggered.connect(lambda: self.add_label_to_item(item))
            delete_action = menu.addAction("Delete")
            delete_action.triggered.connect(lambda: self.drawing_tools.delete_item(item))
            cursor_pos = QtGui.QCursor.pos()
            menu.exec(cursor_pos)
    def add_label_to_item(self, item):
        text, ok = QtWidgets.QInputDialog.getText(
            self, 'Add Label', 'Enter label text:',
            text=self.drawing_tools.item_labels.get(item, "").text() if item in self.drawing_tools.item_labels else ""
        )
        if ok and text:
            self.drawing_tools.add_label_to_item(item, text)

    def undo_drawing(self):
        """Undo the last drawing operation"""
        self.drawing_tools.undo()

    def redo_drawing(self):
        """Redo the last undone drawing operation"""
        self.drawing_tools.redo()

    def add_anchored_text(self):
        """Add anchored text to the chart"""
        # Get the current mouse position or use the center of the view
        view_range = self.plot_widget.viewRange()
        x_center = (view_range[0][0] + view_range[0][1]) / 2
        y_center = (view_range[1][0] + view_range[1][1]) / 2
        pos = QtCore.QPointF(x_center, y_center)

        # Ask for text content
        text, ok = QtWidgets.QInputDialog.getText(
            self, 'Add Anchored Text', 'Enter text:',
            QtWidgets.QLineEdit.EchoMode.Normal)

        if ok and text:
            # Add the anchored text
            text_item = self.drawing_tools.add_anchored_text(pos, text)

            # Highlight the button briefly to show it was activated
            original_style = self.anchored_text_button.styleSheet()

            # Highlight style
            highlight_style = """
                QPushButton {
                    background-color: #3d3d3d;
                    color: white;
                    border: 1px solid #007acc;
                    border-radius: 4px;
                    padding: 5px;
                    text-align: left;
                    font-family: 'Consolas', 'Courier New', monospace;
                    font-weight: bold;
                }
            """

            # Apply highlight
            self.anchored_text_button.setStyleSheet(highlight_style)

            # Create a timer to reset the style after a short delay
            QtCore.QTimer.singleShot(300, lambda: self.anchored_text_button.setStyleSheet(original_style))

            return text_item



    def show_schwab_login(self):
        """Show Schwab login dialog."""
        try:
            from login_page import LoginPage

            # Create a simple dialog to host the login page
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle("Schwab API Login")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QtWidgets.QVBoxLayout(dialog)
            login_page = LoginPage(dialog)
            layout.addWidget(login_page)

            # Add close button
            button_layout = QtWidgets.QHBoxLayout()
            button_layout.addStretch()
            close_btn = QtWidgets.QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            dialog.exec()

        except ImportError as e:
            QtWidgets.QMessageBox.warning(
                self,
                "Import Error",
                f"Could not import Schwab login components: {e}"
            )

    def setup_status_bar(self):
        """Set up custom status bar with Data Source on left and fetch status on right"""
        # Clear any existing status bar message
        self.statusBar().clearMessage()

        # Create custom status bar widget
        status_widget = QtWidgets.QWidget()
        status_layout = QtWidgets.QHBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 2, 10, 2)

        # Data Source label (left side)
        self.data_source_label = QtWidgets.QLabel("Data Source: Historical: Yahoo Finance, Live: Schwab (when available)")
        self.data_source_label.setStyleSheet("color: #a0a0a0; font-family: 'Consolas', 'Courier New', monospace; font-size: 11px;")
        status_layout.addWidget(self.data_source_label)

        # Add stretch to push fetch status to the right
        status_layout.addStretch()

        # Fetch status label (right side)
        self.fetch_status_label = QtWidgets.QLabel("Ready")
        self.fetch_status_label.setStyleSheet("color: #a0a0a0; font-family: 'Consolas', 'Courier New', monospace; font-size: 11px;")
        status_layout.addWidget(self.fetch_status_label)

        # Add the custom widget to the status bar
        self.statusBar().addPermanentWidget(status_widget, 1)

        # Style the status bar
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #1a1a1a;
                border-top: 1px solid #3e3e3e;
            }
        """)

    def update_fetch_status(self, message, color="#a0a0a0"):
        """Update the fetch status on the right side of the status bar"""
        if hasattr(self, 'fetch_status_label'):
            self.fetch_status_label.setText(message)
            self.fetch_status_label.setStyleSheet(f"color: {color}; font-family: 'Consolas', 'Courier New', monospace; font-size: 11px;")

    def on_data_progress(self, value, message):
        """Handle progress updates from the data dispatcher"""
        # Update the fetch status with the progress message
        if "cached data" in message.lower():
            self.update_fetch_status(message, "#55ff55")  # Green for cached data
        elif "error" in message.lower():
            self.update_fetch_status(message, "#ff5555")  # Red for errors
        elif "ready" in message.lower():
            self.update_fetch_status(message, "#55ff55")  # Green for ready
        else:
            self.update_fetch_status(message, "#ffaa00")  # Orange for in-progress

    def update_classifier_settings_from_ml_dialog(self):
        """Update classifier settings from the ML Settings dialog"""
        try:
            # Update classifier settings
            self.crossing_classifier.lookback_window = self.ml_settings_dialog.lookback_spin.value()
            self.crossing_classifier.lookahead_window = self.ml_settings_dialog.lookahead_spin.value()
            self.crossing_classifier.reversal_threshold = self.ml_settings_dialog.reversal_threshold_spin.value()
            self.crossing_classifier.preferred_model_type = self.ml_settings_dialog.model_type_combo.currentText()

            # Update calibration settings
            self.crossing_classifier.use_calibrated_probabilities = self.ml_settings_dialog.enable_calibration.isChecked()

            # Update pivot validator settings
            self.crossing_classifier.use_pivot_validator = self.ml_settings_dialog.use_pivot_validator.isChecked()

            # Update cache settings
            self.crossing_classifier.use_adaptive_cache = self.ml_settings_dialog.adaptive_cache_check.isChecked()
            self.crossing_classifier.cache_size = self.ml_settings_dialog.cache_size_spin.value()

            # Also update the main settings dialog to keep them in sync
            if hasattr(self, 'settings_dialog'):
                if hasattr(self.settings_dialog, 'model_type_combo'):
                    self.settings_dialog.model_type_combo.setCurrentText(self.ml_settings_dialog.model_type_combo.currentText())
                if hasattr(self.settings_dialog, 'lookback_spin'):
                    self.settings_dialog.lookback_spin.setValue(self.ml_settings_dialog.lookback_spin.value())
                if hasattr(self.settings_dialog, 'lookahead_spin'):
                    self.settings_dialog.lookahead_spin.setValue(self.ml_settings_dialog.lookahead_spin.value())
                if hasattr(self.settings_dialog, 'reversal_threshold_spin'):
                    self.settings_dialog.reversal_threshold_spin.setValue(self.ml_settings_dialog.reversal_threshold_spin.value())
                if hasattr(self.settings_dialog, 'confidence_threshold_spin'):
                    self.settings_dialog.confidence_threshold_spin.setValue(self.ml_settings_dialog.confidence_threshold_spin.value())
                if hasattr(self.settings_dialog, 'enable_classifier'):
                    self.settings_dialog.enable_classifier.setChecked(self.ml_settings_dialog.enable_classifier.isChecked())
                if hasattr(self.settings_dialog, 'enable_calibration'):
                    self.settings_dialog.enable_calibration.setChecked(self.ml_settings_dialog.enable_calibration.isChecked())
                if hasattr(self.settings_dialog, 'auto_train_classifier'):
                    self.settings_dialog.auto_train_classifier.setChecked(self.ml_settings_dialog.auto_train_classifier.isChecked())
                if hasattr(self.settings_dialog, 'use_pivot_validator'):
                    self.settings_dialog.use_pivot_validator.setChecked(self.ml_settings_dialog.use_pivot_validator.isChecked())
                if hasattr(self.settings_dialog, 'adaptive_cache_check'):
                    self.settings_dialog.adaptive_cache_check.setChecked(self.ml_settings_dialog.adaptive_cache_check.isChecked())
                if hasattr(self.settings_dialog, 'cache_size_spin'):
                    self.settings_dialog.cache_size_spin.setValue(self.ml_settings_dialog.cache_size_spin.value())
                if hasattr(self.settings_dialog, 'min_cache_size_spin'):
                    self.settings_dialog.min_cache_size_spin.setValue(self.ml_settings_dialog.min_cache_size_spin.value())
                if hasattr(self.settings_dialog, 'max_cache_size_spin'):
                    self.settings_dialog.max_cache_size_spin.setValue(self.ml_settings_dialog.max_cache_size_spin.value())

            print(f"Updated classifier settings: lookback={self.crossing_classifier.lookback_window}, lookahead={self.crossing_classifier.lookahead_window}, threshold={self.crossing_classifier.reversal_threshold}, model_type={self.crossing_classifier.preferred_model_type}")
        except Exception as e:
            print(f"Error updating classifier settings from ML dialog: {str(e)}")
            traceback.print_exc()

    def update_signal_filter_settings_from_ml_dialog(self):
        """Update signal filter settings from the ML Settings dialog"""
        try:
            # Update basic filter settings
            self.confirmation_period = self.ml_settings_dialog.confirmation_period_spin.value()
            self.min_crossing_magnitude = self.ml_settings_dialog.min_crossing_magnitude_spin.value()

            # Update RSI filter settings
            self.use_rsi_filter = self.ml_settings_dialog.use_rsi_filter.isChecked()
            self.rsi_overbought = self.ml_settings_dialog.rsi_overbought_spin.value()
            self.rsi_oversold = self.ml_settings_dialog.rsi_oversold_spin.value()

            # Update other filter settings
            self.use_volatility_adjustment = self.ml_settings_dialog.use_volatility_adjustment.isChecked()
            self.use_high_low_constraint = self.ml_settings_dialog.use_high_low_constraint.isChecked()
            self.use_probabilistic_filtering = self.ml_settings_dialog.use_probabilistic_filtering.isChecked()
            self.enable_signal_filter = self.ml_settings_dialog.enable_signal_filter.isChecked()
            self.enable_pivot_constraint = self.ml_settings_dialog.enable_pivot_constraint.isChecked()

            # Also update the main settings dialog to keep them in sync
            if hasattr(self, 'settings_dialog'):
                if hasattr(self.settings_dialog, 'confirmation_period_spin'):
                    self.settings_dialog.confirmation_period_spin.setValue(self.ml_settings_dialog.confirmation_period_spin.value())
                if hasattr(self.settings_dialog, 'min_crossing_magnitude_spin'):
                    self.settings_dialog.min_crossing_magnitude_spin.setValue(self.ml_settings_dialog.min_crossing_magnitude_spin.value())
                if hasattr(self.settings_dialog, 'use_rsi_filter'):
                    self.settings_dialog.use_rsi_filter.setChecked(self.ml_settings_dialog.use_rsi_filter.isChecked())
                if hasattr(self.settings_dialog, 'rsi_overbought_spin'):
                    self.settings_dialog.rsi_overbought_spin.setValue(self.ml_settings_dialog.rsi_overbought_spin.value())
                if hasattr(self.settings_dialog, 'rsi_oversold_spin'):
                    self.settings_dialog.rsi_oversold_spin.setValue(self.ml_settings_dialog.rsi_oversold_spin.value())
                if hasattr(self.settings_dialog, 'use_volatility_adjustment'):
                    self.settings_dialog.use_volatility_adjustment.setChecked(self.ml_settings_dialog.use_volatility_adjustment.isChecked())
                if hasattr(self.settings_dialog, 'use_high_low_constraint'):
                    self.settings_dialog.use_high_low_constraint.setChecked(self.ml_settings_dialog.use_high_low_constraint.isChecked())
                if hasattr(self.settings_dialog, 'use_probabilistic_filtering'):
                    self.settings_dialog.use_probabilistic_filtering.setChecked(self.ml_settings_dialog.use_probabilistic_filtering.isChecked())
                if hasattr(self.settings_dialog, 'confidence_threshold_spin'):
                    self.settings_dialog.confidence_threshold_spin.setValue(self.ml_settings_dialog.confidence_threshold_spin.value())
                if hasattr(self.settings_dialog, 'enable_signal_filter'):
                    self.settings_dialog.enable_signal_filter.setChecked(self.ml_settings_dialog.enable_signal_filter.isChecked())
                if hasattr(self.settings_dialog, 'enable_pivot_constraint'):
                    self.settings_dialog.enable_pivot_constraint.setChecked(self.ml_settings_dialog.enable_pivot_constraint.isChecked())

            # Redraw the chart
            if self.data is not None:
                self.plot_data_until(self.current_idx)

            print(f"Updated signal filter settings: confirmation_period={self.confirmation_period}, min_crossing_magnitude={self.min_crossing_magnitude}")
        except Exception as e:
            print(f"Error updating signal filter settings from ML dialog: {str(e)}")
            traceback.print_exc()
    def update_classifier_settings(self):
        try:
            # Debug print to verify method is called
            # Removed print statement for updating classifier settings

            # Store previous settings in case of error (dummy classifier doesn't have these attributes)

            # Reset the classifier (dummy classifier doesn't need settings)
            self.crossing_classifier = DummyClassifier()

            # Update UI
            self.crossing_prediction_label.setText("Settings updated - needs retraining")
            self.crossing_prediction_label.setStyleSheet("color: orange;")

            if hasattr(self, 'data') and self.data is not None and len(self.crossing_points) >= 10:
                pass
            else:
                self.crossing_prediction_label.setText("Not enough data for training")
                self.crossing_prediction_label.setStyleSheet("color: yellow;")
        except Exception as e:
            print(f"Error in update_classifier_settings: {str(e)}")
            traceback.print_exc()
    def update_signal_filter_settings(self):
        try:
            # Update basic filter settings
            self.confirmation_period = self.settings_dialog.confirmation_period_spin.value()
            self.min_crossing_magnitude = self.settings_dialog.min_crossing_magnitude_spin.value()

            # Update signal processor settings
            if hasattr(self, 'signal_processor'):
                # Update confirmation period and magnitude threshold
                self.signal_processor.confirmation_period = self.confirmation_period
                self.signal_processor.min_crossing_magnitude = self.min_crossing_magnitude

                # Update RSI thresholds if available
                if hasattr(self.settings_dialog, 'rsi_overbought_spin') and hasattr(self.settings_dialog, 'rsi_oversold_spin'):
                    overbought = self.settings_dialog.rsi_overbought_spin.value()
                    oversold = self.settings_dialog.rsi_oversold_spin.value()
                    logger.info(f"Updated RSI thresholds: overbought={overbought}, oversold={oversold}")

            # Adaptive confirmation settings removed

            print(f"Signal filter settings updated: confirmation={self.confirmation_period}, magnitude={self.min_crossing_magnitude}")

            # Reset any pending crossings
            self.potential_crossings = {}
            if hasattr(self, 'signal_processor'):
                self.signal_processor.current_potential_crossing = None

            # Adaptive confirmation reset code removed

            # Redraw the chart
            if self.data is not None:
                self.plot_data_until(self.current_idx)
        except Exception as e:
            print(f"Error updating signal filter settings: {str(e)}")
            self.crossing_prediction_label.setText(f"Error updating filter settings: {str(e)}")
            self.crossing_prediction_label.setStyleSheet("color: red;")
            logger.error(f"Error in update_signal_filter_settings: {str(e)}")
    def mouseMoved(self, evt):
        pos = evt[0]
        if self.plot_widget.sceneBoundingRect().contains(pos):
            mousePoint = self.plot_widget.plotItem.vb.mapSceneToView(pos)

            # First, check if we're in drawing mode
            if self.drawing_tools.drawing:
                # If we're drawing, update the drawing
                if self.current_drawing_tool == 'text' and self.drawing_tools.current_item:
                    self.drawing_tools.current_item.setPos(mousePoint.x(), mousePoint.y())
                elif self.current_drawing_tool in ['line', 'rectangle', 'horizontal', 'vertical']:
                    self.drawing_tools.update_drawing(mousePoint)

                # Show a different cursor when drawing
                self.plot_widget.setCursor(QtCore.Qt.CursorShape.CrossCursor)

                # Don't update the crosshair or info box while drawing
                return

            # If we're not drawing, update the crosshair
            # Ensure crosshair lines have the correct style (white, dashed, width 2)
            white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
            self.vLine.setPen(white_dashed_pen)
            self.hLine.setPen(white_dashed_pen)

            self.vLine.setPos(mousePoint.x())
            self.hLine.setPos(mousePoint.y())

            # Create crosshair dot if it doesn't exist
            if not hasattr(self, 'crosshair_dot'):
                self.crosshair_dot = pg.ScatterPlotItem()
                self.crosshair_dot.setData([0], [0], size=10,
                                          pen=pg.mkPen('black', width=1.5),
                                          brush=pg.mkBrush('white'))
                self.plot_widget.addItem(self.crosshair_dot, ignoreBounds=True)

            # Update crosshair dot position
            self.crosshair_dot.setData([mousePoint.x()], [mousePoint.y()])

            # Update the right box with candle information
            # Make sure we're using the most up-to-date vector data
            if hasattr(self, '_plot_data_cache') and self._plot_data_cache.get('vector') is None:
                # Recalculate vector if not in cache
                vector = self.calculate_vector(self.data, self.vector_length_spin.value())
                self._plot_data_cache.put('vector', vector)

            self.update_candle_info(mousePoint.x())

            # IMPORTANT: Hide the mouse cursor when hovering over the chart
            # Only if we're not in drawing mode
            if not self.current_drawing_tool:
                self.plot_widget.setCursor(QtCore.Qt.CursorShape.BlankCursor)
            else:
                self.plot_widget.setCursor(QtCore.Qt.CursorShape.CrossCursor)

            index = round(mousePoint.x())
            if self.data is not None and 0 <= index < len(self.data):
                time = self.data.index[index]
                price = self.data.iloc[index]['Close']

                # Calculate real price based on y-axis percentage and pivot
                real_price = price
                if self.current_pivot is not None:
                    # Get the actual price from the original data if available
                    if 0 <= index < len(self.data):
                        # Use the actual price from the original data
                        real_price = price
                    else:
                        # Fallback to percentage calculation if index is out of range
                        real_price = self.current_pivot * (1 + mousePoint.y() / 100)

                    # Update the crosshair price label in the information panel
                    percentage = mousePoint.y()
                    # Calculate converted price from percentage
                    converted_price = self.current_pivot * (1 + percentage / 100)
                    self.crosshair_price_label.setText(f"Crosshair: {converted_price:.2f} ({percentage:.2f}%)")

                # Update the label at the bottom of the screen but don't display it (for functionality)
                self.time_price_label.setText(f"X: {time.strftime('%Y-%m-%d %H:%M:%S')} | Y: {real_price:.2f}")

                # No overlay text for crosshairs - removed as requested

                # Initialize crosshair_overlay attribute if it doesn't exist
                # This is to prevent errors in other parts of the code that might reference it
                if not hasattr(self, 'crosshair_overlay'):
                    self.crosshair_overlay = pg.TextItem(html="", anchor=(0, 0))
                    self.plot_widget.addItem(self.crosshair_overlay)

                # Set empty HTML to hide any text
                self.crosshair_overlay.setHtml("")
            else:
                # Update the label but don't display it (for functionality)
                self.time_price_label.setText("X: -- | Y: --")
                # Hide overlay text if out of range
                if hasattr(self, 'crosshair_overlay'):
                    self.crosshair_overlay.setHtml("")

                # Clear the right box information
                self.clear_candle_info()

                # Restore the default cursor when outside the chart
                if not self.current_drawing_tool:
                    self.plot_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)
                else:
                    self.plot_widget.setCursor(QtCore.Qt.CursorShape.CrossCursor)
        else:
            # Update the label but don't display it (for functionality)
            self.time_price_label.setText("X: -- | Y: --")
            # Hide overlay text if mouse is outside the plot
            if hasattr(self, 'crosshair_overlay'):
                self.crosshair_overlay.setHtml("")

            # Clear the right box information
            self.clear_candle_info()

            # Restore the default cursor when outside the chart
            self.plot_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)
    def update_iv(self):
        if not self.data is None and len(self.data) > 0:
            iv = self.calculate_implied_volatility()
            if iv and self.last_vector_price:
                # Get current text to preserve direction indicator and percentage
                current_text = self.vector_price_label.text()

                # Extract direction indicator if present
                direction_indicator = ""
                if "↑" in current_text:
                    direction_indicator = " ↑"
                elif "↓" in current_text:
                    direction_indicator = " ↓"

                # Extract percentage if present
                percentage_str = ""
                if "(" in current_text and "%)" in current_text:
                    try:
                        percentage_str = current_text.split("(")[1].split(")")[0]
                        percentage_str = f" ({percentage_str})"
                    except:
                        percentage_str = ""

                # Update the label with IV and preserved information but don't display it (for functionality)
                self.vector_price_label.setText(f"Vector: {self.last_vector_price:.2f}{direction_indicator}{percentage_str} | IV: {iv:.2f}%")
    def apply_visual_settings(self):
        """Apply visual settings from the settings dialog"""
        if hasattr(self, 'settings_dialog'):
            # Store settings in variables for easier access
            show_peak_trough_rays = self.settings_dialog.show_peak_trough_rays_check.isChecked()
            show_vectors = self.settings_dialog.show_vectors_check.isChecked()
            show_probability_bands = self.settings_dialog.show_probability_bands_check.isChecked()
            show_imprints = self.settings_dialog.show_imprints_check.isChecked()
            # Pullback and reversal settings removed
            show_clusters = self.settings_dialog.show_clusters_check.isChecked()

            # Apply settings to the UI
            # Create hidden checkboxes if they don't exist
            if not hasattr(self, 'show_peak_trough_rays'):
                self.show_peak_trough_rays = QtWidgets.QCheckBox()
                self.show_peak_trough_rays.setVisible(False)

            if not hasattr(self, 'show_vector'):
                self.show_vector = QtWidgets.QCheckBox()
                self.show_vector.setVisible(False)



            if not hasattr(self, 'show_probability_bands'):
                self.show_probability_bands = QtWidgets.QCheckBox()
                self.show_probability_bands.setVisible(False)

            if not hasattr(self, 'show_imprints'):
                self.show_imprints = QtWidgets.QCheckBox()
                self.show_imprints.setVisible(False)

            # Pullback and reversal checkboxes removed

            if not hasattr(self, 'show_clusters'):
                self.show_clusters = QtWidgets.QCheckBox()
                self.show_clusters.setVisible(False)

            # Set the checkbox states
            self.show_peak_trough_rays.setChecked(show_peak_trough_rays)
            self.show_vector.setChecked(show_vectors)
            self.show_probability_bands.setChecked(show_probability_bands)
            self.show_imprints.setChecked(show_imprints)
            # Pullback and reversal checkbox state setting removed
            self.show_clusters.setChecked(show_clusters)

            # Redraw the chart to apply the changes
            if self.data is not None:
                self.plot_data_until(self.current_idx)

    def sync_imprints_view(self, view_box, ranges):
        # Set X range for imprints widget
        self.imprints_widget.setXRange(0, 1, padding=0)

    def enforce_view_limits(self, view_box, ranges):
        """Enforce view limits: rightmost = second-to-last VISIBLE candle at left edge, leftmost = second VISIBLE candle at right edge"""
        if not hasattr(self, 'data') or self.data is None or self.data.empty:
            return

        try:
            # Get current X range
            x_range = ranges[0]
            current_left = x_range[0]
            current_right = x_range[1]
            view_width = current_right - current_left

            # Get the visible/filtered data that's actually being displayed
            if hasattr(self, 'rebased_data') and self.rebased_data is not None and len(self.rebased_data) > 0:
                visible_data = self.rebased_data
            else:
                # Fallback to filtered data if rebased_data is not available
                visible_data = self.filter_data_by_trading_hours(self.data) if hasattr(self, 'filter_data_by_trading_hours') else self.data

            # Check if visible_data is valid
            if visible_data is None:
                return

            # Handle both list and DataFrame types
            if hasattr(visible_data, 'empty'):  # DataFrame
                if visible_data.empty:
                    return
            elif isinstance(visible_data, list):  # List
                if len(visible_data) == 0:
                    return
            else:
                return  # Unknown type

            # Calculate visible data limits
            visible_length = len(visible_data)
            if visible_length < 2:
                return  # Need at least 2 candles

            # Get the actual indices of visible candles in the chart coordinate system
            # The chart uses indices starting from skip_candles (vector_length + 1)
            from parameter_registry import default_registry
            vector_length = default_registry.get_value('vector_length')
            skip_candles = vector_length + 1

            # Calculate visible candle positions
            second_visible_candle = skip_candles + 1  # Second visible candle position
            second_last_visible_candle = skip_candles + visible_length - 2  # Second-to-last visible candle position

            # Check if we need to enforce limits
            needs_adjustment = False
            new_left = current_left
            new_right = current_right

            # Rightmost limit: second-to-last VISIBLE candle should be at the left edge of view
            # This means current_left should not be greater than second_last_visible_candle
            if current_left > second_last_visible_candle:
                new_left = second_last_visible_candle
                new_right = new_left + view_width
                needs_adjustment = True

            # Leftmost limit: second VISIBLE candle should be at the right edge of view
            # This means current_right should not be less than second_visible_candle
            if current_right < second_visible_candle:
                new_right = second_visible_candle
                new_left = new_right - view_width
                needs_adjustment = True

            # Apply the adjustment if needed
            if needs_adjustment:
                # Temporarily disconnect the signal to avoid recursion
                self.plot_widget.getViewBox().sigRangeChanged.disconnect(self.enforce_view_limits)

                # Set the corrected range
                self.plot_widget.setXRange(new_left, new_right, padding=0)

                # Reconnect the signal
                self.plot_widget.getViewBox().sigRangeChanged.connect(self.enforce_view_limits)

        except Exception as e:
            print(f"Error enforcing view limits: {e}")
            import traceback
            traceback.print_exc()

    # Vector 50 chart completely removed - sync_bottom_chart_view method no longer needed

    def _calculate_candle_density(self, rebased_data, use_full_candle_length=False, num_bins=50):
        """Calculate density of candles across price levels for heatmap visualization"""
        try:
            # Determine the price range for density calculation
            all_highs = []
            all_lows = []

            # Use rebased data
            for candle in rebased_data:
                all_highs.append(candle[2])  # high_pct
                all_lows.append(candle[3])   # low_pct

            if not all_highs or not all_lows:
                return None, None, None

            # Determine the overall price range
            min_price = min(min(all_lows), min(all_highs))
            max_price = max(max(all_lows), max(all_highs))

            # Create bins for density calculation (now customizable)
            bin_edges = np.linspace(min_price, max_price, num_bins + 1)
            bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
            density_counts = np.zeros(num_bins)

            # Calculate density based on candle coverage
            for candle in rebased_data:
                high_pct = candle[2]
                low_pct = candle[3]

                if use_full_candle_length:
                    # Use full candle length - sample points along the entire candle
                    candle_range = high_pct - low_pct
                    if candle_range > 0:
                        # Sample multiple points along the candle
                        num_samples = max(1, int(candle_range * 10))  # More samples for larger candles
                        sample_points = np.linspace(low_pct, high_pct, num_samples)

                        for point in sample_points:
                            # Find which bin this point belongs to
                            bin_idx = np.digitize(point, bin_edges) - 1
                            if 0 <= bin_idx < num_bins:
                                density_counts[bin_idx] += 1.0 / num_samples
                    else:
                        # Single point candle
                        point = (high_pct + low_pct) / 2
                        bin_idx = np.digitize(point, bin_edges) - 1
                        if 0 <= bin_idx < num_bins:
                            density_counts[bin_idx] += 1.0
                else:
                    # Use current sampling method - weight by candle range
                    candle_range = high_pct - low_pct
                    candle_center = (high_pct + low_pct) / 2

                    # Find the bin for the candle center
                    bin_idx = np.digitize(candle_center, bin_edges) - 1
                    if 0 <= bin_idx < num_bins:
                        # Weight by candle range (larger candles contribute more)
                        weight = max(0.1, candle_range)  # Minimum weight to ensure all candles contribute
                        density_counts[bin_idx] += weight

            # Normalize the density counts
            if density_counts.max() > 0:
                density_counts = density_counts / density_counts.max()

            return bin_centers, density_counts, bin_edges

        except Exception as e:
            logger.debug(f"Error calculating candle density: {e}")
            return None, None, None

    def _add_density_heatmap_to_chart(self, bin_centers, density_counts, bin_edges, x_range):
        """Add density heatmap visualization to the chart"""
        try:
            if bin_centers is None or density_counts is None or bin_edges is None:
                return

            # Calculate heatmap positioning
            x_min, x_max = x_range
            bar_width = x_max - x_min + 5  # Span the full width of the chart with some padding
            bar_x_start = x_min - 2.5  # Start slightly before the first candle

            # White color for brighter density visualization
            white_color = (255, 255, 255)  # Pure white

            # Create horizontal bars with opacity based on density
            for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                if density < 0.001:  # Lower threshold for more visible density bars
                    continue

                # Create a white rectangle spanning the chart width
                rect = QtWidgets.QGraphicsRectItem(
                    bar_x_start,
                    bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                    bar_width,
                    bin_edges[i+1] - bin_edges[i]  # Height is bin size
                )

                # Set opacity based on density with brighter, more visible gradients
                min_opacity = 30   # Higher minimum opacity for better visibility
                max_opacity = 220  # Much higher maximum for solid white appearance

                # Apply square root transformation to boost lower densities for better visibility
                boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                # Create white color with calculated opacity (solid white to clear gradient)
                heatmap_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                rect.setBrush(pg.mkBrush(heatmap_color))

                # No border for cleaner look
                rect.setPen(pg.mkPen(None))

                # Set z-value to render behind candles
                rect.setZValue(-1)

                # Add the rectangle to the chart (it will render behind candles due to z-order)
                self.plot_widget.addItem(rect)

            logger.debug(f"Added bright white density heatmap with {len([d for d in density_counts if d >= 0.001])} visible density bars")

        except Exception as e:
            logger.debug(f"Error adding density heatmap to chart: {e}")

    def _add_density_profile_to_chart(self, bin_centers, density_counts, bin_edges, x_range):
        """Add density profile visualization to the chart (white bars on the left side)"""
        try:
            if bin_centers is None or density_counts is None or bin_edges is None:
                return

            # Calculate profile positioning
            x_min, x_max = x_range
            profile_max_width = 15.0  # Much larger maximum width for more exaggerated profile
            profile_x_start = x_min - 20.0  # Position much further to the left to accommodate larger bars

            # Create density profile bars with width based on density
            for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                # Skip bins with very low density for cleaner visualization
                if density < 0.001:  # Lower threshold for more visible density bars (matching heatmap)
                    continue

                # Calculate bar width based on density
                profile_width = density * profile_max_width

                # Create a white rectangle with width based on density
                profile_rect = QtWidgets.QGraphicsRectItem(
                    profile_x_start,
                    bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                    profile_width,
                    bin_edges[i+1] - bin_edges[i]  # Height is bin size
                )

                # Set color to match density heatmap gradient (white with opacity based on density)
                # Use the same white color and opacity calculation as the heatmap for consistency
                white_color = (255, 255, 255)  # Pure white (same as heatmap)

                # Set opacity based on density with brighter, more visible gradients (matching heatmap)
                min_opacity = 30   # Higher minimum opacity for better visibility (same as heatmap)
                max_opacity = 220  # Much higher maximum for solid white appearance (same as heatmap)

                # Apply square root transformation to boost lower densities for better visibility (same as heatmap)
                boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                # Create white color with calculated opacity (solid white to clear gradient, matching heatmap exactly)
                profile_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                profile_rect.setBrush(pg.mkBrush(profile_color))

                # No border for cleaner look (matching heatmap style)
                profile_rect.setPen(pg.mkPen(None))

                # Set z-value to render behind candles
                profile_rect.setZValue(-1)

                # Add the density profile rectangle to the chart
                self.plot_widget.addItem(profile_rect)

            logger.debug(f"Added bright white density profile with {len([d for d in density_counts if d >= 0.001])} visible profile bars")

        except Exception as e:
            logger.debug(f"Error adding density profile to chart: {e}")




    def toggle_imprints_visibility(self):
        # Get performance mode setting
        display_options = self.settings_dialog.get_display_options() if hasattr(self, 'settings_dialog') else {}
        performance_mode = display_options.get('performance_mode', False)

        # Set visibility based on checkbox state
        self.imprints_widget.setVisible(self.show_imprints.isChecked())

        if self.show_imprints.isChecked() and hasattr(self, '_cached_pivot_transitions'):
            # Use lazy loading - only draw imprints when they're visible
            if performance_mode:
                # In performance mode, use animation to make the transition smoother
                self.imprints_widget.setWindowOpacity(0.0)
                self.imprints_widget.setVisible(True)

                # Draw imprints
                self.draw_imprints(self._cached_pivot_transitions)

                # Fade in with animation
                self.imprints_fade = QtCore.QPropertyAnimation(self.imprints_widget, b"windowOpacity")
                self.imprints_fade.setDuration(150)  # Shorter animation in performance mode
                self.imprints_fade.setStartValue(0.0)
                self.imprints_fade.setEndValue(1.0)
                self.imprints_fade.start()
            else:
                # Normal mode - just draw imprints
                self.draw_imprints(self._cached_pivot_transitions)
        else:
            # Clear all imprint items
            if hasattr(self, 'imprint_lines'):
                for line in self.imprint_lines:
                    self.imprints_widget.removeItem(line)
            if hasattr(self, 'imprint_labels'):
                for label in self.imprint_labels:
                    self.imprints_widget.removeItem(label)
            if hasattr(self, 'imprint_clusters'):
                for cluster in self.imprint_clusters:
                    self.plot_widget.removeItem(cluster)

            # Reset lists
            self.imprint_lines = []
            self.imprint_labels = []
            self.imprint_clusters = []

            # Clear cache if it exists
            if hasattr(self, '_imprint_cache'):
                self._imprint_cache.clear()

    def update_ml_settings_only(self):
        """Update ML settings without triggering recalculations or retraining"""
        try:
            # Dummy classifier doesn't need settings updates
            print("ML settings update skipped (using dummy classifier)")

        except Exception as e:
            print(f"Error in update_ml_settings_only: {str(e)}")
            traceback.print_exc()

    def update_signal_visibility(self):
        # Redraw the chart to apply the changes
        self.plot_data_until(self.current_idx)

    def create_signals_legend(self):
        # Remove any existing legend
        if hasattr(self, 'legend_item') and self.legend_item is not None:
            self.plot_widget.removeItem(self.legend_item)
            self.legend_item = None

        # Legend creation removed (pullback and reversal signals removed)
        return

        # Create a LegendItem
        self.legend_item = pg.LegendItem((150, 60), offset=(30, 30))
        self.legend_item.setParentItem(self.plot_widget.getPlotItem())

        # Position in the top-right corner
        self.legend_item.anchor(itemPos=(1, 0), parentPos=(1, 0), offset=(-10, 10))




    def draw_imprints(self, pivot_transitions):
        # Check if imprints should be shown based on settings
        if not self.show_imprints.isChecked():
            return

        # Initialize imprint cache if it doesn't exist
        if not hasattr(self, '_imprint_cache'):
            cache_size = 100  # Default cache size
            if hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'imprint_cache_size_spin'):
                cache_size = self.settings_dialog.imprint_cache_size_spin.value()
            self._imprint_cache = SimpleCache(cache_size)

        # Get display options
        display_options = self.settings_dialog.get_display_options() if hasattr(self, 'settings_dialog') else {}
        performance_mode = display_options.get('performance_mode', False)
        simplified_imprints = display_options.get('simplified_imprints', False)

        # Create a cache key based on the current view and transitions
        x_range = self.imprints_widget.viewRange()[0]
        cache_key = (
            str(pivot_transitions),  # String representation of transitions
            x_range[0],              # Left edge of view
            x_range[1],              # Right edge of view
            performance_mode,        # Performance mode setting
            simplified_imprints      # Simplified imprints setting
        )

        # Check if we have a cached result
        cached_result = self._imprint_cache.get(cache_key) if hasattr(self, '_imprint_cache') else None
        if cached_result is not None:
            # Use cached result
            self.imprint_lines, self.imprint_labels, self.imprint_clusters = cached_result
            return

        # Clear existing items
        if hasattr(self, 'imprint_lines'):
            for line in self.imprint_lines:
                self.imprints_widget.removeItem(line)
        if hasattr(self, 'imprint_labels'):
            for label in self.imprint_labels:
                self.imprints_widget.removeItem(label)
        if hasattr(self, 'imprint_clusters'):
            for cluster in self.imprint_clusters:
                self.plot_widget.removeItem(cluster)

        # Initialize empty lists
        self.imprint_lines = []
        self.imprint_labels = []
        self.imprint_clusters = []

        # Return if no transitions
        if not pivot_transitions:
            return

        # Clear the widget
        self.imprints_widget.clear()

        # Get view range
        left_edge = x_range[0]
        right_edge = x_range[1]

        # Add title (only in normal mode)
        if not performance_mode:
            title = pg.TextItem(
                html='<div style="color: #FFFFFF; font-size: 9pt; font-weight: bold;">P/T</div>',
                anchor=(0.5, 0)
            )
            title.setPos((left_edge + right_edge) / 2, self.plot_widget.viewRange()[1][1])
            self.imprints_widget.addItem(title)
            self.imprint_labels.append(title)

        # Sort transitions
        sorted_transitions = sorted(pivot_transitions, key=lambda x: x[0])

        # Get cluster settings
        cluster_threshold = self.settings_dialog.cluster_threshold_spin.value() if hasattr(self, 'settings_dialog') else 0.15
        min_cluster_size = self.settings_dialog.min_cluster_size_spin.value() if hasattr(self, 'settings_dialog') else 3
        show_clusters = self.show_clusters.isChecked() and self.settings_dialog.enable_clusters.isChecked() if hasattr(self, 'settings_dialog') else self.show_clusters.isChecked()
        cluster_color = self.settings_dialog.cluster_color_combo.currentText() if hasattr(self, 'settings_dialog') else "yellow"
        cluster_opacity = self.settings_dialog.cluster_opacity_spin.value() if hasattr(self, 'settings_dialog') else 50

        # Skip clusters in performance mode
        if performance_mode:
            show_clusters = False

        # Find clusters
        clusters = []
        if show_clusters and not simplified_imprints:
            current_cluster = []
            for i, (level, cycle_type, is_closed) in enumerate(sorted_transitions):
                if not current_cluster:
                    current_cluster.append((level, cycle_type, is_closed))
                else:
                    last_level = current_cluster[-1][0]
                    if abs(level - last_level) <= cluster_threshold:
                        current_cluster.append((level, cycle_type, is_closed))
                    else:
                        if len(current_cluster) >= min_cluster_size:
                            clusters.append(current_cluster)
                        current_cluster = [(level, cycle_type, is_closed)]
            if len(current_cluster) >= min_cluster_size:
                clusters.append(current_cluster)

        # Simplified rendering for large datasets or performance mode
        if simplified_imprints or performance_mode:
            # Reduce the number of imprints in simplified mode
            max_imprints = 10 if performance_mode else 20
            if len(sorted_transitions) > max_imprints:
                # Keep only the most significant imprints
                # Sort by absolute level value (distance from zero)
                significant_transitions = sorted(sorted_transitions, key=lambda x: abs(x[0]), reverse=True)
                sorted_transitions = significant_transitions[:max_imprints]

        # Draw imprint lines
        for level, cycle_type, is_closed in sorted_transitions:
            # Determine line color based on cycle type
            if cycle_type == "bullish":
                line_color = "#00FF00" if is_closed else "#90EE90"
            else:
                line_color = "#FF6666" if is_closed else "#FF3333"

            # Draw line
            imprint_line = self.imprints_widget.plot(
                [left_edge, right_edge], [level, level],
                pen=pg.mkPen(color=line_color, width=2 if performance_mode else 3)
            )
            self.imprint_lines.append(imprint_line)

            # Add price label (skip in performance mode)
            if self.current_pivot is not None and not performance_mode:
                actual_price = self.current_pivot * (1 + level / 100)
                price_str = f"{actual_price:,.2f}"

                # Determine text color
                if cycle_type == "bullish":
                    text_color = "#00FF00" if is_closed else "#90EE90"
                else:
                    text_color = "#FF6666" if is_closed else "#FF3333"

                # Create and add label - only show (Target) for untouched (open) P/T
                label_text = f"{price_str} (Target)" if not is_closed else price_str
                label = pg.TextItem(
                    html=f'<div style="background-color: rgba(0,0,0,0.7); padding: 1px 3px; border-radius: 3px; font-size: 8pt; color: {text_color};">{label_text}</div>',
                    anchor=(0.5, 0.5)
                )
                label.setPos((left_edge + right_edge) / 2, level)
                self.imprints_widget.addItem(label)
                self.imprint_labels.append(label)

        # Draw clusters (skip in performance mode)
        if show_clusters and not performance_mode:
            color_map = {
                "yellow": (255, 255, 0),
                "red": (255, 0, 0),
                "green": (0, 255, 0),
                "blue": (0, 0, 255),
                "cyan": (0, 255, 255),
                "magenta": (255, 0, 255)
            }

            for cluster in clusters:
                min_level = min(level for level, _, _ in cluster)
                max_level = max(level for level, _, _ in cluster)
                avg_level = sum(level for level, _, _ in cluster) / len(cluster)

                # Create polygon coordinates
                x_data = [0, len(self.data), len(self.data), 0, 0]
                y_data = [min_level, min_level, max_level, max_level, min_level]

                # Get color
                rgb = color_map.get(cluster_color, (255, 255, 0))

                # Create and add cluster rectangle
                cluster_rect = pg.PlotDataItem(
                    x=x_data,
                    y=y_data,
                    pen=pg.mkPen(color=cluster_color, width=2, style=QtCore.Qt.PenStyle.DashLine),
                    fillLevel=min_level,
                    brush=pg.mkBrush(color=(*rgb, cluster_opacity))
                )

                if self.current_pivot is not None:
                    min_price = self.current_pivot * (1 + min_level / 100)
                    max_price = self.current_pivot * (1 + max_level / 100)
                    cluster_text = f"Cluster ({len(cluster)}): {min_price:.2f} - {max_price:.2f}"

                    # Create and add cluster label
                    cluster_label = pg.TextItem(
                        html=f'<div style="background-color: rgba(0,0,0,0.7); padding: 2px 4px; border-radius: 3px; font-size: 9pt; color: {cluster_color};">{cluster_text}</div>',
                        anchor=(0, 0.5)
                    )
                    cluster_label.setPos(len(self.data) * 0.95, avg_level)

                    # Add to plot
                    self.plot_widget.addItem(cluster_rect)
                    self.plot_widget.addItem(cluster_label)

                    # Store for later removal
                    self.imprint_clusters.extend([cluster_rect, cluster_label])

        # Cache the result
        if hasattr(self, '_imprint_cache'):
            self._imprint_cache.put(cache_key, (self.imprint_lines, self.imprint_labels, self.imprint_clusters))
    def disable_ml_functionality(self):
        """Temporarily disable ML functionality by saving and modifying classifier state"""
        self._original_classifier = None
        self._original_is_trained = False

        # Save the original classifier and its state
        if hasattr(self, 'crossing_classifier'):
            self._original_classifier = self.crossing_classifier
            self._original_is_trained = self.crossing_classifier.is_trained

            # Create a dummy classifier that doesn't do anything
            class DummyClassifier:
                def __init__(self):
                    self.is_trained = False

                def predict(self, *args, **kwargs):
                    return None

                def train(self, *args, **kwargs):
                    return False

            # Replace the real classifier with the dummy one
            self.crossing_classifier = DummyClassifier()

        # Set the skip ML calculations flag
        self._skip_ml_calculations = True

        print("ML functionality temporarily disabled")

    def restore_ml_functionality(self):
        """Restore ML functionality by restoring the original classifier"""
        # Restore the original classifier if it was saved
        if hasattr(self, '_original_classifier') and self._original_classifier is not None:
            self.crossing_classifier = self._original_classifier
            self.crossing_classifier.is_trained = self._original_is_trained

        # Reset the skip ML calculations flag
        self._skip_ml_calculations = False

        # Clean up
        if hasattr(self, '_original_classifier'):
            delattr(self, '_original_classifier')
        if hasattr(self, '_original_is_trained'):
            delattr(self, '_original_is_trained')

        print("ML functionality restored")

    def open_settings_dialog(self):
        # Initialize auto_fetch_check with the current state of auto_fetch
        if hasattr(self, 'auto_fetch') and hasattr(self.settings_dialog, 'auto_fetch_check'):
            self.settings_dialog.auto_fetch_check.setChecked(self.auto_fetch.isChecked())

        # Initialize visual settings checkboxes with the current state of the UI
        if hasattr(self, 'settings_dialog'):
            # Initialize peak/trough rays checkbox
            if hasattr(self, 'show_rays') and hasattr(self.settings_dialog, 'show_peak_trough_rays_check'):
                self.settings_dialog.show_peak_trough_rays_check.setChecked(self.show_rays.isChecked())

            # Initialize vectors checkbox
            if hasattr(self, 'show_vectors') and hasattr(self.settings_dialog, 'show_vectors_check'):
                self.settings_dialog.show_vectors_check.setChecked(self.show_vectors.isChecked())

            # Initialize probability bands checkbox
            if hasattr(self, 'show_probability_bands') and hasattr(self.settings_dialog, 'show_probability_bands_check'):
                self.settings_dialog.show_probability_bands_check.setChecked(self.show_probability_bands.isChecked())

            # Initialize imprints checkbox
            if hasattr(self, 'show_imprints') and hasattr(self.settings_dialog, 'show_imprints_check'):
                self.settings_dialog.show_imprints_check.setChecked(self.show_imprints.isChecked())

            # Pullback and reversal checkbox initialization removed

            # Initialize clusters checkbox
            if hasattr(self, 'show_clusters') and hasattr(self.settings_dialog, 'show_clusters_check'):
                self.settings_dialog.show_clusters_check.setChecked(self.show_clusters.isChecked())

        # Temporarily disable ML functionality
        self.disable_ml_functionality()

        result = self.settings_dialog.exec()

        # Restore ML functionality if dialog was cancelled
        if result != QtWidgets.QDialog.DialogCode.Accepted:
            self.restore_ml_functionality()
            return

        # If we get here, the dialog was accepted
        new_colors = self.settings_dialog.get_chart_colors()
        # Save the current background color
        current_background = self.chart_colors['background']
        # Update colors but preserve the background color
        self.chart_colors = new_colors
        # Set background color to dark grey
        self.chart_colors['background'] = '#1e1e1e'
        # Set background directly to dark grey
        self.plot_widget.setBackground('#1e1e1e')
        if hasattr(self, 'candlestick_item') and self.candlestick_item is not None:
            self.candlestick_item.updateColors(
                self.chart_colors['bullish'],
                self.chart_colors['bearish']
            )
            # Update signals with new colors
            if hasattr(self.candlestick_item, 'signals') and self.candlestick_item.signals:
                self.candlestick_item.updateSignals(self.candlestick_item.signals)
        # Update the vector price label color
        if hasattr(self, 'vector_price_label'):
            self.vector_price_label.setStyleSheet(f"color: {self.chart_colors['vector']}; font-weight: bold; font-size: 10px;")

        # Apply auto-fetch setting from settings dialog to the auto-fetch checkbox
        if hasattr(self, 'auto_fetch') and hasattr(self.settings_dialog, 'auto_fetch_check'):
            auto_fetch_enabled = self.settings_dialog.auto_fetch_check.isChecked()
            if self.auto_fetch.isChecked() != auto_fetch_enabled:
                self.auto_fetch.setChecked(auto_fetch_enabled)

        # Apply visual settings
        self.apply_visual_settings()

        self.save_chart_settings()

        # Call vector_length_changed with skip_ml_calculations=True to prevent ML recalculations
        self.vector_length_changed(skip_ml_calculations=True)

        # Update ML settings without triggering recalculations
        self.update_ml_settings_only()

        # Don't reload the chart - wait for fetch data button

        # Show a message to inform the user to press fetch data button
        reply = QtWidgets.QMessageBox.question(
            self,
            "Settings Applied",
            "Settings have been applied. Would you like to fetch data to reload the chart with the new settings?",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            QtWidgets.QMessageBox.StandardButton.Yes
        )

        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            # Trigger fetch data
            self.fetch_data()
        else:
            # Just update the chart with current data if available (for trading hours changes)
            if hasattr(self, 'data') and self.data is not None and not self.data.empty:
                self.plot_data_until(self.current_idx)

        # Restore ML functionality
        self.restore_ml_functionality()
    def clear_imprint_cache(self):
        """Clear the imprint cache to free up memory"""
        if hasattr(self, '_imprint_cache'):
            self._imprint_cache.clear()
            print(f"Cleared imprint cache")

    def save_chart_settings(self):
        settings = QtCore.QSettings('VectorRebaseChart', 'ChartAppearance')
        # Always save the background color as dark grey
        settings.setValue('background_color', '#1e1e1e')
        settings.setValue('bullish_color', self.chart_colors['bullish'])
        settings.setValue('bearish_color', self.chart_colors['bearish'])
        settings.setValue('vector_color', self.chart_colors['vector'])

        # Save vector length setting
        if hasattr(self, 'vector_length_spin'):
            settings.setValue('vector_length', self.vector_length_spin.value())

        # Preset functionality has been removed
        # Initialize current_preset attribute if it doesn't exist
        if not hasattr(self, 'current_preset'):
            self.current_preset = ""

        settings.sync()
    def load_chart_settings(self):
        settings = QtCore.QSettings('VectorRebaseChart', 'ChartAppearance')
        # Always set background color to dark grey regardless of saved settings
        self.chart_colors['background'] = '#1e1e1e'
        if settings.contains('bullish_color'):
            self.chart_colors['bullish'] = settings.value('bullish_color')
        if settings.contains('bearish_color'):
            self.chart_colors['bearish'] = settings.value('bearish_color')
        if settings.contains('vector_color'):
            self.chart_colors['vector'] = settings.value('vector_color')

        # Load vector length setting
        if settings.contains('vector_length'):
            # Store the value to be applied after vector_length_spin is created
            self._vector_length_value = int(settings.value('vector_length', 20))
        # Preset functionality has been removed
    def update_crosshair(self, event):
        """Update crosshair position based on mouse movement"""
        # Get the mouse position in plot coordinates
        pos = event[0]
        if self.plot_widget.sceneBoundingRect().contains(pos):
            # Convert mouse position to plot coordinates
            mouse_point = self.plot_widget.getPlotItem().vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()

            # Make sure crosshairs are visible
            self.crosshair_v.setVisible(True)
            self.crosshair_h.setVisible(True)

            # Now we want to show the crosshair text with percentage from extrema
            self.crosshair_text.setVisible(True)

            # Ensure crosshair lines have the correct style (white, dashed, width 2)
            white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
            self.crosshair_v.setPen(white_dashed_pen)
            self.crosshair_h.setPen(white_dashed_pen)

            # Update crosshair lines
            self.crosshair_v.setPos(x)
            self.crosshair_h.setPos(y)

            # Format the date/time for x-axis if we have data (for internal use only)
            date_str = "--"
            if hasattr(self, 'data') and self.data is not None and len(self.data) > 0:
                # Convert x position to data index using round() for consistency
                idx = round(x)
                if 0 <= idx < len(self.data):
                    # Get the timestamp at this index
                    timestamp = self.data.index[idx]
                    date_str = timestamp.strftime('%Y-%m-%d %H:%M')

            # Calculate price if pivot is available
            price_str = "--"
            if hasattr(self, 'current_pivot') and self.current_pivot is not None:
                # Get the most recent extrema price if available
                extrema_price = None
                if hasattr(self, 'extrema_prices') and self.extrema_prices and hasattr(self, 'current_idx'):
                    # Find the most recent extrema change before current index
                    most_recent_extrema_idx = None
                    for extrema_idx, price in self.extrema_prices.items():
                        if extrema_idx <= self.current_idx and (most_recent_extrema_idx is None or extrema_idx > most_recent_extrema_idx):
                            most_recent_extrema_idx = extrema_idx
                            extrema_price = price

                # Update current_pivot if extrema_price is available
                if extrema_price is not None:
                    self.current_pivot = extrema_price

                # Calculate price using current_pivot
                price = self.current_pivot * (1 + y / 100)
                price_str = f"{price:.2f}"

                # Update the crosshair price label in the information panel
                # Calculate converted price from percentage
                converted_price = self.current_pivot * (1 + y / 100)
                self.crosshair_price_label.setText(f"Crosshair: {converted_price:.2f} ({y:.2f}%)")

            # Update the time/price label in the UI but don't display it (for functionality)
            self.time_price_label.setText(f"Time: {date_str} | Price: {price_str}")

            # IMPORTANT: Hide the mouse cursor when hovering over the chart
            # This is a direct implementation with no try/except to ensure it works
            # Only set cursor to blank at the widget level
            self.plot_widget.setCursor(QtCore.Qt.CursorShape.BlankCursor)
        else:
            # Hide crosshairs when mouse is outside the plot
            self.crosshair_v.setVisible(False)
            self.crosshair_h.setVisible(False)
            self.crosshair_text.setVisible(False)

            # Restore the default cursor when outside the chart
            self.plot_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

    def on_timeframe_changed(self, timeframe):
        """Handle timeframe changes and update the parameter registry."""
        # Update the parameter registry
        default_registry.set_value('timeframe', timeframe)

        # Call the original method to update the days to load maximum value
        self.update_dtl_max_value(timeframe)

    def on_days_to_load_changed(self, value):
        """Handle days to load changes and update the parameter registry."""
        # Update the parameter registry
        default_registry.set_value('days_to_load', value)

    def on_parameter_registry_changed(self, name, value):
        """Handle changes to the parameter registry from other tabs."""
        if name == 'timeframe' and self.timeframe_combo.currentText() != value:
            self.timeframe_combo.setCurrentText(value)
        elif name == 'vector_length' and self.vector_length_spin.value() != value:
            self.vector_length_spin.setValue(value)
        elif name == 'days_to_load' and self.dtl_spin.value() != value:
            self.dtl_spin.setValue(value)
        elif name == 'symbol' and hasattr(self, 'symbol_input') and self.symbol_input.text() != value:
            self.symbol_input.setText(value)



    def get_trading_hours_setting(self):
        """Get the current trading hours setting"""
        if hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'trading_hours_group'):
            if self.settings_dialog.rth_radio.isChecked():
                return 'rth'
            elif self.settings_dialog.eth_radio.isChecked():
                return 'eth'
            else:
                return 'both'
        else:
            return 'both'  # Default fallback

    def filter_data_by_trading_hours(self, data):
        """Filter data based on trading hours setting"""
        if data is None or data.empty:
            return data

        trading_hours = self.get_trading_hours_setting()
        if trading_hours == 'both':
            return data  # Return all data

        # Convert index to datetime if it's not already
        if not isinstance(data.index, pd.DatetimeIndex):
            try:
                data_copy = data.copy()
                data_copy.index = pd.to_datetime(data_copy.index)
            except:
                # If conversion fails, return original data
                return data
        else:
            data_copy = data.copy()

        # Ensure timezone-aware datetime index (assume ET timezone for US markets)
        if data_copy.index.tz is None:
            try:
                # Assume the data is in ET timezone
                data_copy.index = data_copy.index.tz_localize('US/Eastern')
            except:
                # If localization fails, return original data
                return data
        else:
            # Convert to ET timezone
            try:
                data_copy.index = data_copy.index.tz_convert('US/Eastern')
            except:
                return data

        # Define regular trading hours (9:30 AM - 4:00 PM ET)
        rth_start = pd.Timestamp('09:30:00').time()
        rth_end = pd.Timestamp('16:00:00').time()

        if trading_hours == 'rth':
            # Filter for regular trading hours only
            mask = (data_copy.index.time >= rth_start) & (data_copy.index.time <= rth_end)
            # Also filter for weekdays only (Monday=0, Sunday=6)
            mask = mask & (data_copy.index.weekday < 5)
            return data_copy[mask]
        elif trading_hours == 'eth':
            # Filter for extended trading hours only (everything except RTH)
            mask = ~((data_copy.index.time >= rth_start) & (data_copy.index.time <= rth_end) & (data_copy.index.weekday < 5))
            return data_copy[mask]

        return data



    def _fetch_yfinance_with_extended_hours(self, symbol, interval, days_to_load):
        """Fetch data from Yahoo Finance with extended hours included"""
        try:
            import yfinance as yf

            ticker = yf.Ticker(symbol)
            period = f"{days_to_load}d"

            # Fetch data with extended hours (prepost=True)
            data = ticker.history(period=period, interval=interval, prepost=True)

            if data.empty:
                # Fallback to regular hours if extended hours fails
                print("Extended hours data not available, falling back to regular hours")
                data = ticker.history(period=period, interval=interval, prepost=False)

            return data

        except Exception as e:
            print(f"Error fetching extended hours data: {e}")
            # Fallback to regular yfinance fetch
            try:
                import yfinance as yf
                ticker = yf.Ticker(symbol)
                period = f"{days_to_load}d"
                return ticker.history(period=period, interval=interval)
            except Exception as fallback_error:
                print(f"Fallback fetch also failed: {fallback_error}")
                raise fallback_error

    def update_data_from_universal(self, symbol, data):
        """Update the chart with data from the universal control panel."""
        # If symbol is a DataFrame, it means data was passed directly
        if isinstance(symbol, pd.DataFrame):
            data = symbol
            # Try to get symbol from data attributes if available
            if hasattr(data, 'attrs') and 'symbol' in data.attrs:
                symbol = data.attrs['symbol']
                self.symbol_input.setText(symbol)
        else:
            # Update the symbol input
            self.symbol_input.setText(symbol)

        # Store the data
        self.data = data

        # Update the chart
        self.current_idx = len(data) - 1
        self.plot_data_until(self.current_idx)

        # Update the candlestick chart
        self.update_candlestick_chart()

        # Emit the data_fetched signal to notify other components
        self.data_fetched.emit(symbol, self.timeframe_combo.currentText(), self.dtl_spin.value())

    def update_dtl_max_value(self, timeframe):
        # Define maximum periods for different timeframes
        # For daily, weekly, or monthly timeframes, we don't set a maximum
        max_periods = {
            "1m": 7,
            "2m": 60,
            "5m": 60,
            "15m": 60,
            "30m": 60,
            "60m": 365
        }

        # For daily, weekly, or monthly timeframes, set a very high maximum (effectively no limit)
        if timeframe in ["1d", "1wk", "1mo"]:
            self.dtl_spin.setMaximum(20000)  # Increased to allow 15k+ days
        elif timeframe in max_periods:
            self.dtl_spin.setMaximum(max_periods[timeframe])
            if self.dtl_spin.value() > max_periods[timeframe]:
                self.dtl_spin.setValue(max_periods[timeframe])
                # Update the parameter registry with the new value
                default_registry.set_value('days_to_load', self.dtl_spin.value())
    def clear_candle_info(self):
        """Clear the candle information in the right box"""
        self.idx_label.setText("IDX: --")
        self.date_label.setText("Date: --")
        self.open_label.setText("Open: --")
        self.high_label.setText("High: --")
        self.low_label.setText("Low: --")
        self.close_label.setText("Close: --")
        self.volume_label.setText("Volume: --")
        self.current_pivot_label.setText("Current Extrema: --")
        self.candle_pivot_label.setText("Prev Cycle Extrema: --")
        self.line_price_label.setText("Line Price: -- (--)")
        self.crosshair_price_label.setText("Crosshair: -- (--)")
        self.avg_peak_idx_label.setText("Avg Peak Cycle: --")
        self.avg_trough_idx_label.setText("Avg Trough Cycle: --")

    def calculate_average_peak_trough_indices(self):
        """Calculate average cycle numbers for peaks and troughs based on category data"""
        if not hasattr(self, 'data') or self.data is None or len(self.data) == 0:
            return None, None

        try:
            # Check if we have the percentage_based_categories stored in this instance
            if not hasattr(self, 'percentage_based_categories') or not self.percentage_based_categories:
                return None, None

            categories = self.percentage_based_categories

            peak_cycle_numbers = []
            trough_cycle_numbers = []

            # Process each category to extract cycle numbers
            for idx, category in categories.items():
                if not category or category == "":
                    continue

                # Extract cycle number and type (H for high/peak, L for low/trough)
                if category.endswith('H'):
                    # Extract the cycle number (everything before 'H')
                    cycle_num_str = category[:-1]
                    try:
                        cycle_num = int(cycle_num_str)
                        peak_cycle_numbers.append(cycle_num)
                    except ValueError:
                        continue
                elif category.endswith('L'):
                    # Extract the cycle number (everything before 'L')
                    cycle_num_str = category[:-1]
                    try:
                        cycle_num = int(cycle_num_str)
                        trough_cycle_numbers.append(cycle_num)
                    except ValueError:
                        continue

            # Calculate averages of cycle numbers
            avg_peak_cycle = sum(peak_cycle_numbers) / len(peak_cycle_numbers) if peak_cycle_numbers else None
            avg_trough_cycle = sum(trough_cycle_numbers) / len(trough_cycle_numbers) if trough_cycle_numbers else None

            return avg_peak_cycle, avg_trough_cycle

        except Exception as e:
            return None, None

    def update_candle_info(self, x_pos):
        """Update the right box with candle information at the given x position"""
        if self.data is None or len(self.data) == 0:
            return

        # Check for NaN or invalid x_pos values
        import math
        if math.isnan(x_pos) or math.isinf(x_pos):
            return

        # Convert x position to index using round() for more accurate selection
        idx = round(x_pos)
        if idx < 0 or idx >= len(self.data):
            return

        # Get candle data
        candle = self.data.iloc[idx]
        date = self.data.index[idx]

        # Format values
        date_str = date.strftime("%Y-%m-%d %H:%M")
        open_val = f"{candle['Open']:.2f}"
        high_val = f"{candle['High']:.2f}"
        low_val = f"{candle['Low']:.2f}"
        close_val = f"{candle['Close']:.2f}"
        volume_val = f"{candle['Volume']:,.0f}"

        # Get current pivot (extrema price - the price of the line right before it changes direction)
        current_pivot = "--"

        # First check if we have extrema_prices and find the most recent extrema before this candle
        if hasattr(self, 'extrema_prices') and self.extrema_prices:
            # Find the most recent extrema change before this candle
            most_recent_extrema_idx = None
            extrema_price = None

            for extrema_idx, price in self.extrema_prices.items():
                if extrema_idx <= idx and (most_recent_extrema_idx is None or extrema_idx > most_recent_extrema_idx):
                    most_recent_extrema_idx = extrema_idx
                    extrema_price = price

            if extrema_price is not None:
                # Update the current_pivot to match the extrema_price to ensure consistency
                self.current_pivot = extrema_price
                current_pivot = f"{extrema_price:.2f}"
            # Fallback to current_pivot if no extrema found
            elif hasattr(self, 'current_pivot') and self.current_pivot is not None:
                current_pivot = f"{self.current_pivot:.2f}"
        # Fallback to current_pivot if no extrema_prices
        elif hasattr(self, 'current_pivot') and self.current_pivot is not None:
            current_pivot = f"{self.current_pivot:.2f}"

        # Get the last candle close price of the previous cycle
        candle_pivot = "--"
        if hasattr(self, 'cycle_changes') and self.cycle_changes:
            # Find the most recent cycle change before this candle
            prev_cycle_end = None
            for cycle_change_idx in self.cycle_changes:
                if cycle_change_idx < idx:
                    prev_cycle_end = cycle_change_idx
                else:
                    break

            # If we found a previous cycle end, get the close price of that candle
            if prev_cycle_end is not None and prev_cycle_end < len(self.data):
                prev_cycle_close = self.data.iloc[prev_cycle_end]['Close']
                candle_pivot = f"{prev_cycle_close:.2f}"

        # Get the line price for this candle if available
        line_price = "--"

        # First try to get the vector from the cache for the most up-to-date value
        if hasattr(self, '_plot_data_cache') and self._plot_data_cache.get('vector') is not None:
            vector = self._plot_data_cache.get('vector')
            if idx < len(vector):
                actual_line_price = vector.iloc[idx]
                # Calculate percentage from pivot
                if hasattr(self, 'current_pivot') and self.current_pivot is not None:
                    vector_percentage = ((actual_line_price / self.current_pivot) - 1) * 100
                    line_price = f"{actual_line_price:.2f} ({vector_percentage:.2f}%)"
        # Fallback to rebased_vector if cache is not available
        elif hasattr(self, 'rebased_vector') and self.rebased_vector and idx < len(self.rebased_vector):
            vector_percentage = self.rebased_vector[idx]
            if hasattr(self, 'current_pivot') and self.current_pivot is not None:
                actual_line_price = self.current_pivot * (1 + vector_percentage / 100.0)
                line_price = f"{actual_line_price:.2f} ({vector_percentage:.2f}%)"

        # Calculate average peak and trough cycle numbers
        avg_peak_cycle, avg_trough_cycle = self.calculate_average_peak_trough_indices()

        # Format average cycle numbers (rounded to whole numbers)
        avg_peak_str = f"{round(avg_peak_cycle)}" if avg_peak_cycle is not None else "--"
        avg_trough_str = f"{round(avg_trough_cycle)}" if avg_trough_cycle is not None else "--"

        # Update labels
        self.idx_label.setText(f"IDX: {idx}")
        self.date_label.setText(f"Date: {date_str}")
        self.open_label.setText(f"Open: {open_val}")
        self.high_label.setText(f"High: {high_val}")
        self.low_label.setText(f"Low: {low_val}")
        self.close_label.setText(f"Close: {close_val}")
        self.volume_label.setText(f"Volume: {volume_val}")
        self.current_pivot_label.setText(f"Current Extrema: {current_pivot}")
        self.candle_pivot_label.setText(f"Candle Extrema: {candle_pivot}")
        self.line_price_label.setText(f"Line Price: {line_price}")
        self.avg_peak_idx_label.setText(f"Avg Peak Cycle: {avg_peak_str}")
        self.avg_trough_idx_label.setText(f"Avg Trough Cycle: {avg_trough_str}")

    def eventFilter(self, obj, event):
        """Event filter to handle resize events for the plot_widget"""
        if obj == self.plot_widget and event.type() == QtCore.QEvent.Type.Resize:
            # Update the right_box height to match the plot_widget height
            self.right_box.setFixedHeight(self.plot_widget.height())
        return super().eventFilter(obj, event)

    def update_threshold_labels(self):
        """Update the position of threshold labels and redraw zones when view range changes"""
        x_range = self.plot_widget.viewRange()[0]
        x_max = x_range[1]

        # Update the position of the text labels
        for text in self.threshold_labels:
            if hasattr(text, 'myThreshold'):
                text.setPos(x_max, text.myThreshold)

        # Only redraw the zones if we have data and imprints
        if hasattr(self, 'rebased_data') and self.rebased_data and hasattr(self, '_cached_pivot_transitions') and self._cached_pivot_transitions:
            # Remove old threshold lines
            old_lines = self.threshold_lines
            for line in old_lines:
                self.plot_widget.removeItem(line)
            self.threshold_lines = []

            # Recalculate the bands based on imprints
            # Sort imprints by level
            sorted_transitions = sorted(self._cached_pivot_transitions, key=lambda x: x[0])

            # Separate bullish and bearish imprints
            bullish_imprints = [level for level, cycle_type, _ in sorted_transitions if cycle_type == "bullish" and level > 0]
            bearish_imprints = [level for level, cycle_type, _ in sorted_transitions if cycle_type == "bearish" and level < 0]

            # Sort bullish imprints in descending order (highest to lowest)
            bullish_imprints.sort(reverse=True)

            # Sort bearish imprints in ascending order (lowest to highest)
            bearish_imprints.sort()

            # Calculate percentage bands using the new formula: Tp = h((1-p)(n+1))
            # where p is the desired continuation probability, n is the number of observations,
            # and h() is the order-statistic function (interpolation in sorted data)
            bullish_levels = {}
            bearish_levels = {}

            def calculate_threshold_level(sorted_data, p):
                """
                Calculate threshold level using the formula Tp = h((1-p)(n+1))

                Args:
                    sorted_data: List of sorted values (ascending for bearish, descending for bullish)
                    p: Desired continuation probability (0.1 for 10%, 0.2 for 20%, etc.)

                Returns:
                    Interpolated threshold value
                """
                if not sorted_data or len(sorted_data) == 0:
                    return None

                n = len(sorted_data)
                # Calculate the rank: (1-p)(n+1)
                rank = (1 - p) * (n + 1)

                # Handle edge cases
                if rank <= 1:
                    return sorted_data[0]
                elif rank >= n:
                    return sorted_data[-1]

                # Interpolate between adjacent values
                lower_index = int(rank) - 1  # Convert to 0-based indexing
                upper_index = min(lower_index + 1, n - 1)

                # Calculate interpolation weight
                weight = rank - int(rank)

                # Linear interpolation
                if lower_index == upper_index:
                    return sorted_data[lower_index]
                else:
                    return sorted_data[lower_index] * (1 - weight) + sorted_data[upper_index] * weight

            def convert_percentage_to_vector_based(percentage_level, current_pivot, vector_price):
                """
                Convert a percentage level from extrema-based to vector-based

                Args:
                    percentage_level: The percentage level relative to extrema (pivot)
                    current_pivot: The current extrema price
                    vector_price: The current vector price

                Returns:
                    The equivalent percentage level relative to the vector
                """
                if current_pivot is None or vector_price is None:
                    return percentage_level

                # Convert percentage to actual price using extrema
                actual_price = current_pivot * (1 + percentage_level / 100)

                # Convert back to percentage using vector as reference
                vector_based_percentage = ((actual_price / vector_price) - 1) * 100

                return vector_based_percentage

            # Get current price and vector price for conditional logic
            current_price = None
            vector_price = None

            # Get current price
            if hasattr(self, 'data') and self.data is not None and not self.data.empty:
                current_price = self.data['Close'].iloc[-1]

            # Get vector price
            if hasattr(self, '_plot_data_cache') and self._plot_data_cache.get('vector') is not None:
                vector = self._plot_data_cache.get('vector')
                if len(vector) > 0:
                    vector_price = vector.iloc[-1]
            elif hasattr(self, 'last_vector_price') and self.last_vector_price is not None:
                vector_price = self.last_vector_price

            # Determine if current price is above or below extrema
            price_above_extrema = False
            if current_price is not None and self.current_pivot is not None:
                price_above_extrema = current_price > self.current_pivot

            # Calculate bullish bands if we have enough imprints
            if len(bullish_imprints) > 0:
                # Sort bullish imprints in ascending order for the formula
                bullish_sorted = sorted(bullish_imprints)

                # Calculate thresholds for different probability levels
                probabilities = [
                    (0.1, "Bullish 10%"),
                    (0.2, "Bullish 20%"),
                    (0.5, "Bullish 50%"),
                    (0.8, "Bullish 80%")
                ]

                for p, label in probabilities:
                    threshold = calculate_threshold_level(bullish_sorted, p)
                    if threshold is not None:
                        # Store the original threshold without conversion
                        # The conditional pricing logic will be applied during display
                        bullish_levels[label] = threshold

            # Calculate bearish bands if we have enough imprints
            if len(bearish_imprints) > 0:
                # Sort bearish imprints in descending order (from least negative to most negative)
                # This is because for bearish levels, we want the "highest" (least negative) values
                # to correspond to lower probabilities
                bearish_sorted = sorted(bearish_imprints, reverse=True)

                # Calculate thresholds for different probability levels
                probabilities = [
                    (0.1, "Bearish 10%"),
                    (0.2, "Bearish 20%"),
                    (0.5, "Bearish 50%"),
                    (0.8, "Bearish 80%")
                ]

                for p, label in probabilities:
                    threshold = calculate_threshold_level(bearish_sorted, p)
                    if threshold is not None:
                        # Store the original threshold without conversion
                        # The conditional pricing logic will be applied during display
                        bearish_levels[label] = threshold

            # Create new lines with the current view range
            view_range = self.plot_widget.viewRange()
            x_range = view_range[0]

            # Create lines for bullish levels
            for key, value in bullish_levels.items():
                # Add a line for the level
                line = pg.InfiniteLine(pos=value, angle=0, movable=False,
                                     pen=pg.mkPen(color='blue', width=1, style=QtCore.Qt.PenStyle.DotLine))
                self.plot_widget.addItem(line)
                self.threshold_lines.append(line)

            # Create lines for bearish levels
            for key, value in bearish_levels.items():
                # Add a line for the level
                line = pg.InfiniteLine(pos=value, angle=0, movable=False,
                                     pen=pg.mkPen(color='red', width=1, style=QtCore.Qt.PenStyle.DotLine))
                self.plot_widget.addItem(line)
                self.threshold_lines.append(line)

            # Use the shared get_pricing_reference method for consistency with other systems

            # Check if we need to create new labels
            existing_labels = set()
            for text in self.threshold_labels:
                if hasattr(text, 'myThreshold'):
                    label_text = text.toPlainText()
                    key = label_text.split(':')[0].strip()
                    existing_labels.add(key)

                    if key in bullish_levels:
                        value = bullish_levels[key]
                        text.myThreshold = value
                        reference_price = self.get_pricing_reference(value)
                        actual_price = reference_price * (1 + value/100)
                        reference_type = "V" if reference_price == vector_price else "E"
                        text.setText(f"{key}: {value:.2f}% (${actual_price:.2f})")
                        text.setPos(x_max, value)
                    elif key in bearish_levels:
                        value = bearish_levels[key]
                        text.myThreshold = value
                        reference_price = self.get_pricing_reference(value)
                        actual_price = reference_price * (1 + value/100)
                        reference_type = "V" if reference_price == vector_price else "E"
                        text.setText(f"{key}: {value:.2f}% (${actual_price:.2f})")
                        text.setPos(x_max, value)

            # Create new labels for any levels that don't have labels yet
            for key, value in bullish_levels.items():
                if key not in existing_labels:
                    reference_price = self.get_pricing_reference(value)
                    actual_price = reference_price * (1 + value/100)
                    reference_type = "V" if reference_price == vector_price else "E"
                    text = pg.TextItem(text=f"{key}: {value:.2f}% (${actual_price:.2f})", color='blue', anchor=(1,0.5))
                    text.myThreshold = value
                    text.setPos(x_max, value)
                    self.plot_widget.addItem(text)
                    self.threshold_labels.append(text)

            for key, value in bearish_levels.items():
                if key not in existing_labels:
                    reference_price = self.get_pricing_reference(value)
                    actual_price = reference_price * (1 + value/100)
                    reference_type = "V" if reference_price == vector_price else "E"
                    text = pg.TextItem(text=f"{key}: {value:.2f}% (${actual_price:.2f})", color='red', anchor=(1,0.5))
                    text.myThreshold = value
                    text.setPos(x_max, value)
                    self.plot_widget.addItem(text)
                    self.threshold_labels.append(text)


    def main(self):
        pass


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    window = VectorRebaseChart()
    window.show()
    sys.exit(app.exec())