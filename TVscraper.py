"""
TradingView Data Scraper - Standalone GUI Version
A complete, self-contained TradingView data scraper with PyQt6 GUI interface.

*** USES ZERO YAHOO FINANCE - ONLY TRADINGVIEW SOURCES ***

This file contains all required components:
- Configuration
- WebSocket scraper (TradingView WebSocket API)
- TVDataFeed scraper (TradingView unofficial API - optional)
- Data processor
- Main scraper class
- PyQt6 GUI interface with pyqtgraph charts (BLACK BACKGROUND)

DATA SOURCES:
- TradingView WebSocket API (wss://data.tradingview.com/socket.io/websocket)
- TVDataFeed library (TradingView unofficial API)
- NO YAHOO FINANCE OR OTHER SOURCES

Dependencies:
    pip install requests websocket-client pandas numpy python-dotenv retrying PyQt6 pyqtgraph matplotlib

Optional (for TVDataFeed method):
    pip install git+https://github.com/rongardF/tvdatafeed.git
"""

import os
import json
import time
import logging
import threading
import re
import sys
from datetime import datetime
from typing import Optional, Dict, Any, List, Union
import pandas as pd
import numpy as np
import websocket
from retrying import retry
from dotenv import load_dotenv

# PyQt6 imports
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QGridLayout, QLabel, QLineEdit,
                            QComboBox, QPushButton, QTextEdit, QProgressBar,
                            QSpinBox, QGroupBox, QSplitter, QMessageBox,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QCheckBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt6.QtGui import QFont, QIcon

# pyqtgraph for charts
import pyqtgraph as pg
from pyqtgraph import PlotWidget, mkPen

# Load environment variables
load_dotenv()

# ============================================================================
# CONFIGURATION
# ============================================================================

class Config:
    """Configuration class for TradingView scraper"""
    
    # TradingView WebSocket URLs
    TRADINGVIEW_WS_URL = "wss://data.tradingview.com/socket.io/websocket"
    TRADINGVIEW_BASE_URL = "https://www.tradingview.com"
    
    # User credentials (optional, for authenticated access)
    TRADINGVIEW_USERNAME = os.getenv("TRADINGVIEW_USERNAME", "")
    TRADINGVIEW_PASSWORD = os.getenv("TRADINGVIEW_PASSWORD", "")
    
    # Rate limiting settings
    REQUEST_DELAY = 1.0  # Seconds between requests
    MAX_RETRIES = 3
    RETRY_DELAY = 2.0
    
    # Data settings
    MAX_BARS = 999999  # Maximum number of bars to fetch (UNLIMITED - fetch all available data)
    DEFAULT_TIMEFRAME = "1D"

    # Session settings for futures and stocks - Default to RTH+ETH (both sessions)
    DEFAULT_EXTENDED_SESSION = "both"

    # Session types
    SESSION_TYPES = {
        "RTH": False,  # Regular Trading Hours only
        "ETH": True,   # Extended Trading Hours (includes pre-market and after-hours)
        "RTH+ETH": "both"  # Fetch both RTH and ETH data separately
    }
    
    # Supported timeframes
    TIMEFRAMES = {
        "1m": "1", "3m": "3", "5m": "5", "15m": "15", "30m": "30",
        "1h": "60", "1H": "60", "2h": "120", "2H": "120", "4h": "240", "4H": "240",
        "6h": "360", "6H": "360", "8h": "480", "8H": "480", "12h": "720", "12H": "720",
        "1D": "1D", "3D": "3D", "1W": "1W", "1M": "1M"
    }
    
    # Supported exchanges (ordered by importance - Mini/Micro contracts first, then major US exchanges)
    EXCHANGES = {
        # === MINI & MICRO CONTRACTS (HIGHEST PRIORITY) ===
        # Note: CME_MINI includes both mini (ES, NQ, YM, RTY) and micro (MES, MNQ, MYM, M2K) contracts
        # Note: NYMEX_MINI includes both mini and micro energy contracts
        # Note: CBOT_MINI includes both mini and micro agricultural contracts
        "CME_MINI": "CME_MINI", "NYMEX_MINI": "NYMEX_MINI", "CBOT_MINI": "CBOT_MINI",
        # === MAJOR US EXCHANGES (SECOND PRIORITY) ===
        "AMEX": "AMEX", "NASDAQ": "NASDAQ", "NYSE": "NYSE",
        # === OTHER FUTURES ===
        "CME": "CME", "GLOBEX": "GLOBEX", "COMEX": "COMEX", "NYMEX": "NYMEX", "CBOT": "CBOT",
        "EUREX": "EUREX",
        # === OTHER US EQUITIES ===
        "BATS": "BATS", "IEX": "IEX",
        # === INTERNATIONAL EQUITIES ===
        "LSE": "LSE", "TSX": "TSX", "EURONEXT": "EURONEXT", "TSE": "TSE", "HKEX": "HKEX",
        "ASX": "ASX", "SSE": "SSE", "SZSE": "SZSE", "NSE": "NSE", "BSE": "BSE",
        # === ETFs AND FUNDS ===
        "ARCA": "ARCA", "BIVA": "BIVA",
        # === OPTIONS & VOLATILITY ===
        "CBOE": "CBOE",
        # === INDICES ===
        "SP": "SP", "DJ": "DJ", "RUSSELL": "RUSSELL",
        # === FOREX ===
        "FX": "FX_IDC", "OANDA": "OANDA",
        # === CRYPTO ===
        "BINANCE": "BINANCE", "COINBASE": "COINBASE", "KRAKEN": "KRAKEN", 
        "BITFINEX": "BITFINEX", "BYBIT": "BYBIT", "KUCOIN": "KUCOIN", "HUOBI": "HUOBI",
        # === ALTERNATIVE/REGIONAL ===
        "OTC": "OTC", "PINK": "PINK"
    }
    
    # Output settings
    OUTPUT_DIR = "data"
    DEFAULT_FORMAT = "csv"
    SUPPORTED_FORMATS = ["csv", "json", "excel"]
    
    # Logging settings
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # WebSocket settings
    WS_TIMEOUT = 30
    WS_PING_INTERVAL = 20
    
    @classmethod
    def get_timeframe_value(cls, timeframe: str) -> str:
        """Get the TradingView timeframe value"""
        return cls.TIMEFRAMES.get(timeframe.upper(), cls.DEFAULT_TIMEFRAME)
    
    @classmethod
    def get_exchange_value(cls, exchange: str) -> str:
        """Get the TradingView exchange value"""
        return cls.EXCHANGES.get(exchange.upper(), exchange.upper())

    @classmethod
    def get_session_value(cls, session: str):
        """Get the session value (bool or 'both')"""
        return cls.SESSION_TYPES.get(session.upper(), cls.DEFAULT_EXTENDED_SESSION)

    @classmethod
    def get_session_display_name(cls, extended_session) -> str:
        """Get display name for session type"""
        if extended_session == "both":
            return "RTH + ETH (Both Sessions)"
        elif extended_session is True:
            return "ETH (Extended Hours)"
        else:
            return "RTH (Regular Hours)"
    
    # Futures contract month codes
    MONTH_CODES = {
        "F": "January", "G": "February", "H": "March", "J": "April",
        "K": "May", "M": "June", "N": "July", "Q": "August",
        "U": "September", "V": "October", "X": "November", "Z": "December"
    }

    @classmethod
    def validate_symbol(cls, symbol: str) -> str:
        """Validate and format symbol"""
        return symbol.upper().strip()

    @classmethod
    def parse_futures_contract(cls, symbol: str) -> dict:
        """Parse futures contract symbol to extract base, month, and year

        Examples:
        - MES1! -> {'base': 'MES', 'type': 'continuous', 'month': None, 'year': None}
        - MESU2025 -> {'base': 'MES', 'type': 'specific', 'month': 'U', 'year': '2025', 'month_name': 'September'}
        - ESU2025 -> {'base': 'ES', 'type': 'specific', 'month': 'U', 'year': '2025', 'month_name': 'September'}
        """
        symbol = symbol.upper().strip()

        # Check if it's a continuous contract (ends with 1!)
        if symbol.endswith('1!'):
            return {
                'base': symbol[:-2],
                'type': 'continuous',
                'month': None,
                'year': None,
                'month_name': None,
                'full_symbol': symbol
            }

        # Try to parse specific contract month (e.g., MESU2025)
        import re
        match = re.match(r'^([A-Z]+)([FGHJKMNQUVXZ])(\d{4})$', symbol)
        if match:
            base, month_code, year = match.groups()
            month_name = cls.MONTH_CODES.get(month_code, "Unknown")
            return {
                'base': base,
                'type': 'specific',
                'month': month_code,
                'year': year,
                'month_name': month_name,
                'full_symbol': symbol
            }

        # If no pattern matches, return as-is
        return {
            'base': symbol,
            'type': 'unknown',
            'month': None,
            'year': None,
            'month_name': None,
            'full_symbol': symbol
        }

    @classmethod
    def get_contract_info(cls, symbol: str) -> str:
        """Get human-readable contract information"""
        info = cls.parse_futures_contract(symbol)

        if info['type'] == 'continuous':
            return f"{info['base']} Continuous Contract"
        elif info['type'] == 'specific':
            return f"{info['base']} {info['month_name']} {info['year']}"
        else:
            return symbol
    
    @classmethod
    def create_output_dir(cls) -> None:
        """Create output directory if it doesn't exist"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)

    # ============================================================================
    # AUTO EXCHANGE DETECTION
    # ============================================================================

    # Symbol patterns for automatic exchange detection
    SYMBOL_EXCHANGE_PATTERNS = {
        # === FUTURES CONTRACTS (HIGHEST PRIORITY) ===
        # Mini/Micro E-mini S&P 500
        r'^(MES|ES)\d*[!]?$|^(MES|ES)[FGHJKMNQUVXZ]\d{4}$': 'CME_MINI',
        # Mini/Micro Nasdaq
        r'^(MNQ|NQ)\d*[!]?$|^(MNQ|NQ)[FGHJKMNQUVXZ]\d{4}$': 'CME_MINI',
        # Mini/Micro Dow Jones
        r'^(MYM|YM)\d*[!]?$|^(MYM|YM)[FGHJKMNQUVXZ]\d{4}$': 'CME_MINI',
        # Mini/Micro Russell 2000
        r'^(M2K|RTY)\d*[!]?$|^(M2K|RTY)[FGHJKMNQUVXZ]\d{4}$': 'CME_MINI',

        # Energy futures (mini/micro)
        r'^(MCL|CL|QM)\d*[!]?$|^(MCL|CL|QM)[FGHJKMNQUVXZ]\d{4}$': 'NYMEX_MINI',
        r'^(MGC|GC|QO)\d*[!]?$|^(MGC|GC|QO)[FGHJKMNQUVXZ]\d{4}$': 'NYMEX_MINI',
        r'^(MSI|SI)\d*[!]?$|^(MSI|SI)[FGHJKMNQUVXZ]\d{4}$': 'NYMEX_MINI',

        # Agricultural futures (mini/micro)
        r'^(MYW|ZW|YW)\d*[!]?$|^(MYW|ZW|YW)[FGHJKMNQUVXZ]\d{4}$': 'CBOT_MINI',
        r'^(MYK|ZC|YC)\d*[!]?$|^(MYK|ZC|YC)[FGHJKMNQUVXZ]\d{4}$': 'CBOT_MINI',
        r'^(MYS|ZS|YS)\d*[!]?$|^(MYS|ZS|YS)[FGHJKMNQUVXZ]\d{4}$': 'CBOT_MINI',

        # Other major futures
        r'^(6E|6A|6B|6C|6J|6S|6N|6L)\d*[!]?$|^(6E|6A|6B|6C|6J|6S|6N|6L)[FGHJKMNQUVXZ]\d{4}$': 'CME',
        r'^(ZB|ZN|ZF|ZT)\d*[!]?$|^(ZB|ZN|ZF|ZT)[FGHJKMNQUVXZ]\d{4}$': 'CBOT',
        r'^(HG|PA|PL)\d*[!]?$|^(HG|PA|PL)[FGHJKMNQUVXZ]\d{4}$': 'COMEX',
        r'^(NG|RB|HO)\d*[!]?$|^(NG|RB|HO)[FGHJKMNQUVXZ]\d{4}$': 'NYMEX',

        # === CRYPTO CURRENCIES ===
        r'^BTC.*USD.*$|^.*BTC.*$': 'COINBASE',
        r'^ETH.*USD.*$|^.*ETH.*$': 'COINBASE',
        r'^ADA.*USD.*$|^.*ADA.*$': 'COINBASE',
        r'^SOL.*USD.*$|^.*SOL.*$': 'COINBASE',
        r'^DOGE.*USD.*$|^.*DOGE.*$': 'COINBASE',
        r'^LTC.*USD.*$|^.*LTC.*$': 'COINBASE',
        r'^XRP.*USD.*$|^.*XRP.*$': 'COINBASE',

        # === FOREX PAIRS ===
        r'^EUR.*USD$|^GBP.*USD$|^USD.*JPY$|^USD.*CHF$|^AUD.*USD$|^USD.*CAD$|^NZD.*USD$': 'FX_IDC',
        r'^.*USD.*|^.*EUR.*|^.*GBP.*|^.*JPY.*|^.*CHF.*|^.*AUD.*|^.*CAD.*|^.*NZD.*': 'OANDA',

        # === ETFs (MUST COME BEFORE GENERIC STOCK PATTERNS) ===
        # NASDAQ ETFs - High priority
        r'^(QQQ|TQQQ|SQQQ|CQQQ|QTEC|SOXX|SMH|XSD|FTXL|TECL|TECS|SOXL|SOXS|FNGU|FNGD|WEBL|WEBS|CWEB|KWEB|ARKG|ARKK|ARKQ|ARKW|ARKF|ROBO|BOTZ|IRBO|UBOT|THNQ|FINX|IPAY|HACK|CIBR|BUG|SKYY|CLOU|WCLD|FIVG|NXTG|SNSR|DTEC|EDOC|TEMD|GNOM|HELX)$': 'NASDAQ',

        # SPY specifically uses AMEX
        r'^(SPY)$': 'AMEX',

        # ARCA ETFs - High priority (SPY removed and moved to AMEX above)
        r'^(IWM|DIA|VTI|VEA|VWO|AGG|BND|LQD|HYG|JNK|TLT|IEF|SHY|GLD|SLV|USO|UNG|XLE|XLF|XLK|XLV|XLI|XLP|XLY|XLU|XLB|XLRE|VNQ|REIT|EEM|EFA|FXI|EWJ|EWZ|RSX|INDA|ASHR|MCHI|PGJ|VGK|VPL|VGT|VHT|VFH|VIS|VCR|VDC|VAW|VDE|VPU|VTEB|MUB|VMOT|BIV|VCIT|VCSH|VGIT|VGSH|VTIP|SCHP|TIPS|IPE|DBC|DJP|PDBC|GSG|GUNR|PICK|REMX|COPX|SIL|PPLT|PALL|JJC|JJN|JJS|JJT|JJU|JJA|JJG|CORN|WEAT|SOYB|CANE|NIB|COW|CAFE|JO|SGG|BAL|FUE|UGA|UNL|BNO|USL|DNO|SCO|UCO|BOIL|KOLD|UHN|NAT|NGAS|UGAZ|DGAZ|NUGT|DUST|JNUG|JDST|USLV|DSLV|AGQ|ZSL|UGLD|DGLD|GLL|UGL|LABU|LABD|CURE|RXL|XBI|IBB|ICLN|PBW|QCLN|SMOG|FAN|ACES|LIT|BATT|GRID|HAIL|DRIV|IDRV|CARZ|EKAR|KARS|UPRO|SPXU|UDOW|SDOW|TNA|TZA|FAS|FAZ|ERX|ERY|GUSH|DRIP)$': 'ARCA',

        # === US EQUITIES (MAJOR EXCHANGES) ===
        # Tech stocks (NASDAQ)
        r'^(AAPL|MSFT|GOOGL|GOOG|AMZN|TSLA|META|NVDA|NFLX|ADBE|CRM|ORCL|INTC|AMD|QCOM|AVGO|TXN|CSCO|PYPL|CMCSA|PEP|COST|TMUS|SBUX|INTU|ISRG|BKNG|GILD|MDLZ|REGN|FISV|ADP|ATVI|EA|EBAY|WBA|MRNA|ZM|DOCU|PTON|ROKU|SQ|SHOP|SPOT|UBER|LYFT|ABNB|COIN|HOOD|RBLX|SNOW|PLTR|CRWD|ZS|OKTA|DDOG|NET|FSLY|TWLO|WORK|TEAM|ATLASSIAN|MDB|SPLK|VEEV|WDAY|NOW|CRM|ADSK|ANSS|CDNS|SNPS|KLAC|LRCX|AMAT|MU|WDC|STX|NXPI|MXIM|XLNX|MCHP|ADI|ON|MPWR|SWKS|QRVO|CRUS|RMBS|CEVA|SLAB|DIOD|MACOM|ACLS|FORM|POWI|VICR|MTSI|COHU|UCTT|AEIS|EMKR|LFUS|PLAB|AOSL|SMTC|KLIC|AXTI|CCMP|NVMI|ACMR|AEHR|CAMT|ENTG|MKSI|ICHR|ONTO|NNDM|SSYS|DDD|XONE|MTLS|FARO|ZBRA|TER|NOVT|ITRI|COHR|VIAV|LITE|OCLR|INFN|FNSR|AAOI|DSPG|CIEN|JNPR|FFIV|AKAM|VRSN|TTWO|ZNGA|GLUU|GLU|HUYA|BILI|IQ|BIDU|JD|PDD|BABA|TME|NTES|WB|SINA|SOHU|VIPS|YY|MOMO|GRUB|DASH|DKNG|PENN|MGM|LVS|WYNN|CZR|BYD|GNOG|RSI|CHDN|EVRI|ACIC|DGNR|GENI|FUBO|SKLZ|BMBL|MTCH|IAC|ANGI|YELP|GRPN|TRIP|EXPE|BKNG|PCLN|EXPD|CHRW|LSTR|ECHO|HUBG|XPO|ODFL|SAIA|ARCB|YELL|CVNA|VROOM|SFT|ROOT|LMND|OPEN|RDFN|COMP|RKT|UWMC|GHVI|IPOE|SOFI|AFRM|UPST|LC|ONDK|TREE|ENVA|CARG|CARS|AUTO|KAR|IAA|ADESA|CPRT|COPART)$': 'NASDAQ',

        # Traditional large caps (NYSE)
        r'^(JPM|BAC|WFC|C|GS|MS|BLK|SCHW|USB|PNC|TFC|COF|AXP|V|MA|DFS|SYF|PYPL|FIS|FISV|FLT|GPN|WU|WEX|JKHY|TSS|CPAY|EVTC|FLWS|TREE|ENVA|LC|SQ|AFRM|UPST|ONDK|WMT|HD|PG|JNJ|UNH|CVX|XOM|BRK|ABBV|LLY|PFE|TMO|ABT|DHR|MRK|BMY|AMGN|GILD|BIIB|CELG|REGN|VRTX|ILMN|MYGN|BMRN|SRPT|RARE|FOLD|ARWR|EDIT|CRSP|NTLA|BEAM|VERV|PRIME|SGMO|CGEM|RGNX|BLUE|ONCE|RPTX|AVXL|SAVA|ANVS|CTIC|ADMS|AGTC|AGEN|AIMT|AKBA|ALBO|ALEC|ALKS|ALLK|ALNY|ALXN|AMAG|AMAT|AMRN|AMRS|AMTX|ANAB|ANIK|ANTM|APLS|APOG|ARCT|ARDX|ARQL|ARRY|ASMB|ASND|ATHA|ATHX|ATRC|ATRS|AUPH|AVEO|AVIR|AXSM|AYTU|AZRX|BCRX|BDSI|BEAT|BFRA|BGNE|BHAT|BIDU|BIOC|BIOL|BIOS|BKYI|BLCM|BLDP|BLFS|BLPH|BMRA|BNGO|BNTX|BOLD|BPMC|BPTH|BSGM|BSTG|BTAI|BVXV|BYND|BYSI|CAAS|CAPR|CARA|CATB|CBAY|CBIO|CBLI|CBMG|CCCL|CCXI|CDMO|CDNA|CDTX|CDXC|CDXS|CEMI|CERS|CETX|CFMS|CGEM|CHEK|CHMA|CIDM|CLDX|CLLS|CLPT|CLRB|CLSD|CLSN|CLVS|CMRX|CNCE|CNTG|COCP|CODX|COGT|COHN|COMS|CONN|CORT|COTY|CPRX|CRBP|CRDF|CRIS|CRMD|CRSP|CRTX|CRVL|CSCW|CSII|CSTL|CTMX|CTRM|CTSO|CTTC|CTXR|CUTR|CVAC|CVET|CWBR|CXDC|CYAD|CYCC|CYCN|CYTH|CYTK|CZNC|DARE|DFFN|DGLY|DKNG|DLPN|DMAC|DNAD|DNLI|DRRX|DRTT|DSGX|DSSI|DTIL|DXCM|DXPE|DYAI|DYNE|EARS|EAST|EBIX|EBON|ECOR|EDAP|EDIT|EDSA|EFOI|EGAN|EGBN|EGHT|EIGR|EKSO|ELSE|EMBC|EMCG|EMKR|ENDP|ENER|ENLV|ENPH|ENSG|ENTA|ENTG|EOLS|EOSE|EPAY|EQBK|EQIX|EQOS|ERAS|ERIC|ESEA|ESPR|ESSA|ETSY|EURN|EVBG|EVFM|EVGN|EVLO|EVOK|EWBC|EXAS|EXEL|EXFO|EXLS|EXPE|EXPR|EXTR|EYES|EZPW|FAMI|FATE|FBIO|FBNC|FCEL|FDMT|FEIM|FELE|FENC|FGEN|FHTX|FIBK|FINV|FIXX|FLDM|FLGT|FLIC|FLNT|FLWS|FMAO|FMBI|FNCB|FNKO|FOLD|FORD|FORR|FOSL|FOXF|FPAY|FRAF|FRBA|FRGI|FRME|FRPT|FRSX|FSBC|FSBW|FSLR|FTDR|FTFT|FTHM|FTNT|FTRE|FULC|FULT|FUNC|FUSB|FUSN|FVCB|FWRD|FXNC|GABC|GAIA|GAIN|GALT|GAMB|GASL|GATO|GBCI|GBDC|GBIO|GCBC|GDEN|GDOT|GDYN|GECC|GENC|GENE|GEOS|GERN|GEVO|GFED|GGAL|GHDX|GIII|GILT|GLAD|GLBS|GLDD|GLMD|GLNG|GLOG|GLPG|GLPI|GLRE|GLSI|GLUU|GLYC|GMAB|GMDA|GMRE|GNCA|GNMK|GNPX|GNRC|GNSS|GNTX|GNTY|GNUS|GOEV|GOGO|GOLD|GOOD|GOOG|GOOGL|GOSS|GOVX|GPAQ|GPMT|GPOR|GPRE|GPRK|GPRO|GRAB|GRAY|GRBK|GRFS|GRIL|GRIN|GRMN|GRPN|GRTS|GRUB|GRVY|GRWG|GSAT|GSBC|GSIT|GSKY|GSMG|GSOL|GSUM|GTBP|GTES|GTIM|GTLS|GTYH|GULF|GURE|GWGH|GWPH|GWRS|GXGX|GYRO|HAIN|HALL|HALO|HAYN|HBAN|HBCP|HBIO|HBMD|HBNC|HCAC|HCAP|HCAR|HCCO|HCCI|HCDI|HCHC|HCIC|HCKT|HCSG|HDSN|HEAR|HEES|HELE|HEMN|HEPS|HERD|HFBL|HFFG)$': 'NYSE',



        # International stocks
        r'^(BABA|JD|PDD|BIDU|NIO|XPEV|LI|TME|NTES|WB|SINA|SOHU|VIPS|YY|MOMO|IQ|HUYA|BILI|DOYU|KC|TAL|EDU|GSX|COE|LAIX|CDEL|CAAS|TEDU|FEDU|REDU|COUR|UTI|APEI|ATGE|CECO|PRDO|WAFU|DL|PEIX|TOUR|JOBS|SEED|FENG|RENN|SORL|BORN|CBPO|CANG|BEST|YJ|TIGR|FUTU|UP|TUYA|DADA|KC|MNSO|DOYU|HUYA|BILI|IQ|NTES|TME|WB|SINA|SOHU|VIPS|YY|MOMO|BABA|JD|PDD|BIDU|NIO|XPEV|LI)$': 'NASDAQ',

        # Default patterns for common symbols
        r'^[A-Z]{1,5}$': 'NASDAQ',  # Most single-word tickers default to NASDAQ
    }

    @classmethod
    def auto_detect_exchange(cls, symbol: str) -> str:
        """Automatically detect the most appropriate exchange for a given symbol

        Args:
            symbol: The ticker symbol to analyze

        Returns:
            The detected exchange code, or 'CME_MINI' as default for futures-like symbols
        """
        symbol = symbol.upper().strip()

        # Check each pattern in order of priority
        for pattern, exchange in cls.SYMBOL_EXCHANGE_PATTERNS.items():
            if re.match(pattern, symbol, re.IGNORECASE):
                return exchange

        # Default fallback logic
        # If it looks like a futures contract, default to CME_MINI
        if (symbol.endswith('1!') or
            re.match(r'^[A-Z]{2,4}[FGHJKMNQUVXZ]\d{4}$', symbol) or
            len(symbol) <= 4 and symbol.isalpha()):
            return 'CME_MINI'

        # For everything else, default to NASDAQ
        return 'NASDAQ'

    @classmethod
    def get_auto_exchange_info(cls, symbol: str) -> dict:
        """Get detailed information about auto-detected exchange

        Args:
            symbol: The ticker symbol to analyze

        Returns:
            Dictionary with exchange info and confidence level
        """
        detected_exchange = cls.auto_detect_exchange(symbol)
        symbol = symbol.upper().strip()

        # Determine confidence level
        confidence = "medium"
        reason = "Default pattern match"

        # High confidence patterns
        high_confidence_patterns = [
            (r'^(MES|ES|MNQ|NQ|MYM|YM|M2K|RTY)\d*[!]?$', "Mini/Micro futures contract"),
            (r'^(MES|ES|MNQ|NQ|MYM|YM|M2K|RTY)[FGHJKMNQUVXZ]\d{4}$', "Specific futures contract"),
            (r'^BTC.*USD.*$|^ETH.*USD.*$', "Major cryptocurrency pair"),
            (r'^(AAPL|MSFT|GOOGL|AMZN|TSLA|META|NVDA)$', "Major tech stock"),
            (r'^(SPY)$', "Major AMEX ETF"),
            (r'^(IWM|DIA|VTI)$', "Major ARCA ETF"),
            (r'^(QQQ|TQQQ|SQQQ)$', "Major NASDAQ ETF"),
        ]

        for pattern, desc in high_confidence_patterns:
            if re.match(pattern, symbol, re.IGNORECASE):
                confidence = "high"
                reason = desc
                break

        # Low confidence for generic patterns, but NOT for specific ETF matches
        if (re.match(r'^[A-Z]{1,5}$', symbol) and detected_exchange == 'NASDAQ' and
            not re.match(r'^(QQQ|TQQQ|SQQQ|CQQQ|QTEC|SOXX|SMH|XSD|FTXL|TECL|TECS|SOXL|SOXS|FNGU|FNGD|WEBL|WEBS|CWEB|KWEB|ARKG|ARKK|ARKQ|ARKW|ARKF|ROBO|BOTZ|IRBO|UBOT|THNQ|FINX|IPAY|HACK|CIBR|BUG|SKYY|CLOU|WCLD|FIVG|NXTG|SNSR|DTEC|EDOC|TEMD|GNOM|HELX)$', symbol, re.IGNORECASE)):
            confidence = "low"
            reason = "Generic ticker pattern"

        return {
            'symbol': symbol,
            'detected_exchange': detected_exchange,
            'exchange_name': cls.EXCHANGES.get(detected_exchange, detected_exchange),
            'confidence': confidence,
            'reason': reason,
            'all_exchanges': list(cls.EXCHANGES.keys())
        }


# ============================================================================
# DATA PROCESSOR
# ============================================================================

class DataProcessor:
    """Data processing utilities for OHLCV data"""

    @staticmethod
    def validate_ohlcv_data(df: pd.DataFrame) -> bool:
        """Validate OHLCV data structure and content"""
        try:
            if df is None or df.empty:
                return False

            # Check required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                return False

            # Check data types
            for col in required_columns:
                if not pd.api.types.is_numeric_dtype(df[col]):
                    return False

            # Check for valid OHLC relationships
            invalid_ohlc = (
                (df['high'] < df['low']) |
                (df['high'] < df['open']) |
                (df['high'] < df['close']) |
                (df['low'] > df['open']) |
                (df['low'] > df['close'])
            )

            if invalid_ohlc.any():
                return False

            # Check for negative values
            if (df[['open', 'high', 'low', 'close']] <= 0).any().any():
                return False

            if (df['volume'] < 0).any():
                return False

            return True

        except Exception:
            return False

    @staticmethod
    def clean_ohlcv_data(df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize OHLCV data"""
        try:
            if df is None or df.empty:
                return df

            # Remove duplicates
            df = df[~df.index.duplicated(keep='first')]

            # Sort by timestamp
            df = df.sort_index()

            # Remove rows with NaN values
            df = df.dropna()

            # Ensure numeric types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Remove rows where any price is 0 or negative
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df.columns:
                    df = df[df[col] > 0]

            # Ensure volume is non-negative
            if 'volume' in df.columns:
                df = df[df['volume'] >= 0]

            # Fix OHLC relationships
            df['high'] = df[['open', 'high', 'low', 'close']].max(axis=1)
            df['low'] = df[['open', 'high', 'low', 'close']].min(axis=1)

            return df

        except Exception as e:
            logging.error(f"Error cleaning data: {e}")
            return df

    @staticmethod
    def export_data(df: pd.DataFrame, symbol: str, exchange: str,
                   timeframe: str, format: str = "csv") -> str:
        """Export data to various formats"""
        try:
            Config.create_output_dir()

            # Create filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{exchange}_{symbol}_{timeframe}_{timestamp}"

            if format.lower() == "csv":
                filepath = os.path.join(Config.OUTPUT_DIR, f"{filename}.csv")
                df.to_csv(filepath)
            elif format.lower() == "json":
                filepath = os.path.join(Config.OUTPUT_DIR, f"{filename}.json")
                df.to_json(filepath, orient='index', date_format='iso')
            elif format.lower() == "excel":
                filepath = os.path.join(Config.OUTPUT_DIR, f"{filename}.xlsx")
                df.to_excel(filepath)
            else:
                raise ValueError(f"Unsupported format: {format}")

            return filepath

        except Exception as e:
            logging.error(f"Error exporting data: {e}")
            raise


# ============================================================================
# WEBSOCKET SCRAPER
# ============================================================================

class TradingViewWebSocketScraper:
    """TradingView WebSocket data scraper"""

    def __init__(self):
        self.ws = None
        self.connected = False
        self.data_received = []
        self.chart_session = f"cs_{int(time.time() * 1000)}"
        self.quote_session = f"qs_{int(time.time() * 1000)}"
        self.logger = logging.getLogger(__name__)

    def _on_message(self, ws, message: str) -> None:
        """Handle incoming WebSocket messages"""
        try:
            # Handle ping/pong messages
            if re.search(r'~m~\d+~m~~h~\d+', message):
                self.logger.debug("Received ping message, sending pong")
                ws.send(message)
                return

            # Parse data messages
            if "~m~" in message:
                parts = message.split("~m~")
                for i in range(1, len(parts), 2):
                    if i + 1 < len(parts):
                        try:
                            data = json.loads(parts[i + 1])
                            message_type = data.get("m")

                            if message_type == "timescale_update":
                                self.logger.info("Received timescale_update message")
                                self._process_timescale_data(data.get("p", []))
                            elif message_type == "du":
                                self.logger.debug("Received data update (du) message")
                                self._process_data_update(data.get("p", []))
                            elif message_type:
                                self.logger.debug(f"Received message type: {message_type}")
                        except json.JSONDecodeError:
                            continue

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            import traceback
            self.logger.error(f"Message processing error traceback: {traceback.format_exc()}")

    def _process_timescale_data(self, data: List) -> None:
        """Process timescale data from WebSocket"""
        try:
            self.logger.debug(f"Processing timescale data: {len(data)} items")

            if len(data) >= 2 and isinstance(data[1], dict):
                series_data = data[1]

                # Look for series data in different possible locations
                bars_found = False
                bars_processed = 0

                # Check if there's a direct series with data
                for series_key, series_info in series_data.items():
                    if isinstance(series_info, dict):
                        # Look for bars in different possible structures
                        bars = []

                        # Method 1: Check t.s structure (original)
                        if "t" in series_info and isinstance(series_info["t"], dict):
                            bars = series_info["t"].get("s", [])

                        # Method 2: Check direct s structure
                        if not bars and "s" in series_info and isinstance(series_info["s"], list):
                            bars = series_info["s"]

                        # Method 3: Check for any list that looks like OHLCV data
                        if not bars:
                            for key, value in series_info.items():
                                if isinstance(value, list) and len(value) > 0:
                                    # Check if this looks like OHLCV data
                                    if isinstance(value[0], dict) and "v" in value[0]:
                                        bars = value
                                        break

                        # Process bars if found
                        if bars:
                            bars_found = True
                            for bar in bars:
                                if isinstance(bar, dict) and "v" in bar:
                                    bar_data = bar["v"]
                                    if len(bar_data) >= 6:
                                        timestamp = pd.to_datetime(bar_data[0], unit='s')
                                        ohlcv = {
                                            'timestamp': timestamp,
                                            'open': float(bar_data[1]),
                                            'high': float(bar_data[2]),
                                            'low': float(bar_data[3]),
                                            'close': float(bar_data[4]),
                                            'volume': float(bar_data[5])
                                        }
                                        self.data_received.append(ohlcv)
                                        bars_processed += 1

                if bars_found:
                    self.logger.info(f"Successfully processed {bars_processed} bars. Total data points: {len(self.data_received)}")
                else:
                    self.logger.warning("No bars found in timescale data")
            else:
                self.logger.warning(f"Invalid timescale data format")

        except Exception as e:
            self.logger.error(f"Error processing timescale data: {e}")
            import traceback
            self.logger.error(f"Timescale processing error traceback: {traceback.format_exc()}")

    def _process_data_update(self, data: List) -> None:
        """Process data update (du) messages from WebSocket"""
        try:
            if len(data) >= 2 and isinstance(data[1], dict):
                update_data = data[1]

                # Look for series data in the update
                for series_key, series_info in update_data.items():
                    if isinstance(series_info, dict) and "s" in series_info:
                        bars = series_info["s"]
                        if isinstance(bars, list):
                            bars_processed = 0
                            for bar in bars:
                                if isinstance(bar, dict) and "v" in bar:
                                    bar_data = bar["v"]
                                    if len(bar_data) >= 6:
                                        timestamp = pd.to_datetime(bar_data[0], unit='s')
                                        ohlcv = {
                                            'timestamp': timestamp,
                                            'open': float(bar_data[1]),
                                            'high': float(bar_data[2]),
                                            'low': float(bar_data[3]),
                                            'close': float(bar_data[4]),
                                            'volume': float(bar_data[5])
                                        }
                                        self.data_received.append(ohlcv)
                                        bars_processed += 1

                            if bars_processed > 0:
                                self.logger.debug(f"Processed {bars_processed} bars from data update")

        except Exception as e:
            self.logger.error(f"Error processing data update: {e}")

    def _on_error(self, ws, error) -> None:
        """Handle WebSocket errors"""
        self.logger.error(f"WebSocket error: {error}")
        self.logger.error(f"Error type: {type(error)}")
        import traceback
        self.logger.error(f"WebSocket error traceback: {traceback.format_exc()}")

    def _on_close(self, ws, close_status_code, close_msg) -> None:
        """Handle WebSocket close"""
        self.connected = False
        self.logger.info(f"WebSocket connection closed: status_code={close_status_code}, msg={close_msg}")

    def _on_open(self, ws) -> None:
        """Handle WebSocket open"""
        self.connected = True
        self.logger.info("WebSocket connection opened successfully")

        # Send initial messages
        self.logger.info("Sending initial WebSocket messages...")
        self._send_message("set_auth_token", ["unauthorized_user_token"])
        self._send_message("chart_create_session", [self.chart_session, ""])
        self._send_message("quote_create_session", [self.quote_session])
        self.logger.info("Initial WebSocket messages sent")

    def _send_message(self, method: str, params: List) -> None:
        """Send message to WebSocket"""
        try:
            if self.ws and self.connected:
                message = json.dumps({"m": method, "p": params})
                formatted_message = f"~m~{len(message)}~m~{message}"
                self.logger.debug(f"Sending WebSocket message: {method}")
                self.ws.send(formatted_message)
            else:
                self.logger.warning(f"Cannot send message {method}: ws={self.ws is not None}, connected={self.connected}")
        except Exception as e:
            self.logger.error(f"Error sending message {method}: {e}")
            import traceback
            self.logger.error(f"Send message error traceback: {traceback.format_exc()}")

    def connect(self) -> bool:
        """Connect to TradingView WebSocket"""
        try:
            self.logger.info(f"Attempting to connect to TradingView WebSocket: {Config.TRADINGVIEW_WS_URL}")
            self.logger.info(f"Current connection state: connected={self.connected}, ws={self.ws is not None}")

            # Reset connection state
            self.connected = False
            self.data_received = []

            # Create new session IDs to avoid conflicts
            self.chart_session = f"cs_{int(time.time() * 1000)}"
            self.quote_session = f"qs_{int(time.time() * 1000)}"
            self.logger.info(f"Using new session IDs: chart={self.chart_session}, quote={self.quote_session}")

            websocket.enableTrace(False)
            self.ws = websocket.WebSocketApp(
                Config.TRADINGVIEW_WS_URL,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open
            )

            # Start connection in a separate thread
            wst = threading.Thread(target=self.ws.run_forever)
            wst.daemon = True
            wst.start()
            self.logger.info("WebSocket thread started")

            # Wait for connection
            timeout = 10
            while not self.connected and timeout > 0:
                time.sleep(0.5)
                timeout -= 0.5
                self.logger.debug(f"Waiting for connection... timeout={timeout}")

            self.logger.info(f"Connection attempt finished: connected={self.connected}")
            return self.connected

        except Exception as e:
            self.logger.error(f"Failed to connect: {e}")
            import traceback
            self.logger.error(f"Connection error traceback: {traceback.format_exc()}")
            return False

    def disconnect(self) -> None:
        """Disconnect from WebSocket"""
        try:
            self.logger.info("Disconnecting from WebSocket...")
            if self.ws:
                self.ws.close()
                self.connected = False
                self.logger.info("WebSocket disconnected successfully")
                # Give a moment for the connection to close properly
                time.sleep(0.1)
            else:
                self.logger.info("No WebSocket connection to disconnect")
        except Exception as e:
            self.logger.error(f"Error disconnecting: {e}")
            import traceback
            self.logger.error(f"Disconnect error traceback: {traceback.format_exc()}")
        finally:
            # Ensure connection state is reset
            self.connected = False
            self.ws = None

    def get_historical_data(self, symbol: str, exchange: str,
                          timeframe: str = "1D", bars: int = 1000,
                          extended_session = None) -> Optional[pd.DataFrame]:
        """Get historical data via WebSocket"""
        try:
            self.logger.info(f"Starting WebSocket data fetch for {symbol} from {exchange}")
            self.logger.info(f"Parameters: timeframe={timeframe}, bars={bars}, extended_session={extended_session}")

            if not self.connect():
                self.logger.error("Failed to connect to WebSocket")
                return None

            # Clear previous data
            self.data_received = []
            self.logger.info("Cleared previous data, starting fresh")

            # Format symbol
            full_symbol = f"{exchange}:{symbol}"
            tf_value = Config.get_timeframe_value(timeframe)
            self.logger.info(f"Formatted symbol: {full_symbol}, timeframe value: {tf_value}")

            # Log session info
            session_name = Config.get_session_display_name(extended_session)
            if extended_session:
                self.logger.warning(f"Extended session requested for WebSocket method. "
                             f"WebSocket may return all available data regardless of session setting.")
            self.logger.info(f"WebSocket fetching {full_symbol}, session: {session_name}")

            # Request data
            self.logger.info("Sending resolve_symbol message...")
            resolve_params = [
                self.chart_session,
                "symbol_1",
                f"={json.dumps({'symbol': full_symbol, 'adjustment': 'splits'})}"
            ]
            self.logger.debug(f"Resolve symbol params: {resolve_params}")
            self._send_message("resolve_symbol", resolve_params)

            time.sleep(1)  # Wait for symbol resolution
            self.logger.info("Sending create_series message...")

            series_params = [
                self.chart_session,
                "s1",
                "s1",
                "symbol_1",
                tf_value,
                bars
            ]
            self.logger.debug(f"Create series params: {series_params}")
            self._send_message("create_series", series_params)

            # Wait for data
            timeout = Config.WS_TIMEOUT
            self.logger.info(f"Waiting for data... timeout={timeout} seconds")
            while len(self.data_received) == 0 and timeout > 0:
                time.sleep(1)
                timeout -= 1
                if timeout % 5 == 0:  # Log every 5 seconds
                    self.logger.debug(f"Still waiting for data... timeout={timeout}, received={len(self.data_received)} bars")

            self.disconnect()
            self.logger.info(f"Data fetch completed. Received {len(self.data_received)} data points")

            if not self.data_received:
                self.logger.warning("No data received from WebSocket")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(self.data_received)
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()

            self.logger.info(f"WebSocket received {len(df)} bars successfully")
            return df

        except Exception as e:
            self.logger.error(f"Error getting WebSocket data: {e}")
            import traceback
            self.logger.error(f"WebSocket data fetch error traceback: {traceback.format_exc()}")
            self.disconnect()
            return None


# ============================================================================
# TVDATAFEED SCRAPER (OPTIONAL)
# ============================================================================

class TVDataFeedScraper:
    """TradingView data scraper using TVDataFeed library (optional)"""

    def __init__(self, username: str = "", password: str = ""):
        self.username = username
        self.password = password
        self.tv = None
        self.logger = logging.getLogger(__name__)

        try:
            # Try to import and initialize TVDataFeed
            from tvDatafeed import TvDatafeed, Interval

            if username and password:
                self.tv = TvDatafeed(username=username, password=password)
                self.logger.info("TVDataFeed initialized with credentials")
            else:
                self.tv = TvDatafeed()
                self.logger.info("TVDataFeed initialized without credentials")

            # Store interval mappings
            self.intervals = {
                "1m": Interval.in_1_minute, "3m": Interval.in_3_minute, "5m": Interval.in_5_minute,
                "15m": Interval.in_15_minute, "30m": Interval.in_30_minute, "1h": Interval.in_1_hour,
                "2h": Interval.in_2_hour, "4h": Interval.in_4_hour, "1d": Interval.in_daily,
                "1D": Interval.in_daily, "1w": Interval.in_weekly, "1W": Interval.in_weekly,
                "1M": Interval.in_monthly
            }

        except ImportError:
            self.logger.error("TVDataFeed library not found. Install with: pip install git+https://github.com/rongardF/tvdatafeed.git")
            self.tv = None
        except Exception as e:
            self.logger.error(f"Failed to initialize TVDataFeed: {e}")
            self.tv = None

    def is_available(self) -> bool:
        """Check if TVDataFeed is available"""
        return self.tv is not None

    def get_historical_data(self, symbol: str, exchange: str,
                          timeframe: str = "1D", bars: int = 1000,
                          extended_session = False) -> Optional[pd.DataFrame]:
        """Get historical OHLCV data using TVDataFeed"""
        if not self.is_available():
            self.logger.error("TVDataFeed not available")
            return None

        try:
            # Get interval - try both original case and lowercase
            interval = self.intervals.get(timeframe) or self.intervals.get(timeframe.lower())
            if not interval:
                self.logger.error(f"Unsupported timeframe: {timeframe}. Available: {list(self.intervals.keys())}")
                return None

            # Fetch data
            session_name = Config.get_session_display_name(extended_session)
            self.logger.info(f"Fetching {symbol} data from {exchange} with {timeframe} timeframe, session: {session_name}")

            data = self.tv.get_hist(
                symbol=symbol,
                exchange=exchange,
                interval=interval,
                n_bars=bars,
                extended_session=extended_session
            )

            if data is None or data.empty:
                self.logger.warning(f"No data returned for {symbol} from {exchange}")
                return None

            # Standardize column names
            data.columns = data.columns.str.lower()

            self.logger.info(f"TVDataFeed fetched {len(data)} bars for {symbol}")
            return data

        except Exception as e:
            self.logger.error(f"Error fetching data with TVDataFeed: {e}")
            return None


# ============================================================================
# MAIN SCRAPER CLASS
# ============================================================================

# Configure logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format=Config.LOG_FORMAT
)
logger = logging.getLogger(__name__)

class TradingViewScraper:
    """Main TradingView scraper with multiple fallback methods"""

    def __init__(self, username: str = "", password: str = ""):
        self.username = username or Config.TRADINGVIEW_USERNAME
        self.password = password or Config.TRADINGVIEW_PASSWORD

        # Initialize scrapers
        self.tvdatafeed_scraper = TVDataFeedScraper(self.username, self.password)
        self.websocket_scraper = TradingViewWebSocketScraper()

        # Track which methods are available
        self.available_methods = self._check_available_methods()

        logger.info(f"TradingView Scraper initialized. Available methods: {self.available_methods}")

    def _check_available_methods(self) -> List[str]:
        """Check which scraping methods are available"""
        methods = []

        if self.tvdatafeed_scraper.is_available():
            methods.append("tvdatafeed")

        # WebSocket is always available (built-in)
        methods.append("websocket")

        return methods

    @retry(stop_max_attempt_number=Config.MAX_RETRIES,
           wait_fixed=Config.RETRY_DELAY * 1000)
    def get_historical_data(self, symbol: str, exchange: str,
                          timeframe: str = "1D", bars: int = 1000,
                          method: str = "auto", validate: bool = True,
                          extended_session = None) -> Optional[pd.DataFrame]:
        """Get historical OHLCV data with multiple fallback methods"""
        try:
            # Validate inputs
            symbol = Config.validate_symbol(symbol)
            exchange = Config.get_exchange_value(exchange)

            # Handle session parameter - default to RTH+ETH (both sessions)
            if extended_session is None:
                extended_session = "both"  # Default to both sessions

            # Handle "both" sessions - for GUI, we'll use ETH (extended)
            if extended_session == "both":
                logger.info("Both sessions requested - using ETH (extended) for GUI version")
                extended_session = True

            # Determine methods to try
            if method == "auto":
                methods_to_try = self.available_methods.copy()
            elif method in self.available_methods:
                methods_to_try = [method]
            else:
                logger.error(f"Method '{method}' not available. Available: {self.available_methods}")
                return None

            data = None

            for scraping_method in methods_to_try:
                try:
                    logger.info(f"Trying method: {scraping_method}")

                    if scraping_method == "tvdatafeed":
                        data = self.tvdatafeed_scraper.get_historical_data(
                            symbol, exchange, timeframe, bars, extended_session
                        )
                    elif scraping_method == "websocket":
                        data = self.websocket_scraper.get_historical_data(
                            symbol, exchange, timeframe, bars, extended_session
                        )

                    if data is not None and not data.empty:
                        logger.info(f"Successfully fetched data using {scraping_method}")
                        break
                    else:
                        logger.warning(f"No data returned from {scraping_method}")

                except Exception as e:
                    logger.error(f"Error with {scraping_method}: {e}")
                    continue

            if data is None or data.empty:
                logger.error("All scraping methods failed")
                return None

            # Process data
            if validate:
                if not DataProcessor.validate_ohlcv_data(data):
                    logger.warning("Data validation failed, but returning data anyway")

                data = DataProcessor.clean_ohlcv_data(data)

            # Add rate limiting
            time.sleep(Config.REQUEST_DELAY)

            logger.info(f"Successfully fetched {len(data)} bars for {symbol}")
            return data

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            raise

    def export_data(self, data: pd.DataFrame, symbol: str, exchange: str,
                   timeframe: str, format: str = "csv") -> str:
        """Export data to file"""
        return DataProcessor.export_data(data, symbol, exchange, timeframe, format)

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        # Clean up any resources if needed
        if hasattr(self.websocket_scraper, 'disconnect'):
            self.websocket_scraper.disconnect()


# ============================================================================
# GUI COMPONENTS
# ============================================================================

class DataFetchWorker(QThread):
    """Worker thread for fetching data without blocking the GUI"""

    # Signals
    progress = pyqtSignal(str)
    data_ready = pyqtSignal(object, str, str, str)  # data, symbol, exchange, timeframe
    error = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, symbol, exchange, timeframe, days_to_load, username="", password=""):
        super().__init__()
        self.symbol = symbol
        self.exchange = exchange
        self.timeframe = timeframe
        self.days_to_load = days_to_load
        self.username = username
        self.password = password
        self.is_running = True

    def run(self):
        try:
            self.progress.emit(f"Initializing scraper...")

            with TradingViewScraper(self.username, self.password) as scraper:
                self.progress.emit(f"Fetching {self.symbol} from {self.exchange}...")

                # Request more bars than needed to ensure we get enough data
                # Use maximum available if days is very high (unlimited request)
                if self.days_to_load >= 9999:
                    bars = Config.MAX_BARS  # Fetch maximum available data
                    self.progress.emit(f"Fetching maximum available historical data...")
                else:
                    # Request significantly more bars to ensure we get the requested days
                    # For daily data: request 3x the days to account for weekends/holidays
                    # For intraday: request even more to account for market hours
                    if self.timeframe.lower() == '1d':
                        bars = self.days_to_load * 3  # Account for weekends/holidays
                    else:
                        bars = self._calculate_bars_from_days(self.days_to_load, self.timeframe) * 2

                    # Ensure reasonable limits
                    bars = max(bars, 50)  # Minimum 50 bars
                    bars = min(bars, 5000)  # Maximum 5000 bars (TradingView limit)

                data = scraper.get_historical_data(
                    symbol=self.symbol,
                    exchange=self.exchange,
                    timeframe=self.timeframe,
                    bars=bars,
                    validate=True,
                    extended_session="both"  # Default to RTH+ETH (both sessions)
                )

                if data is not None and not data.empty:
                    # Filter data to get exactly the requested number of days
                    filtered_data = self._filter_data_by_days(data, self.days_to_load, self.timeframe)
                    self.progress.emit(f"Successfully fetched {len(filtered_data)} bars for {self.days_to_load} days")
                    self.data_ready.emit(filtered_data, self.symbol, self.exchange, self.timeframe)
                else:
                    self.error.emit("No data received")

        except Exception as e:
            self.error.emit(f"Error: {str(e)}")
        finally:
            self.finished.emit()

    def _calculate_bars_from_days(self, days: int, timeframe: str) -> int:
        """Calculate approximate number of bars from days based on timeframe"""
        # Use realistic trading hours calculation (similar to data_dispatcher.py)
        # Approximate bars per day for different timeframes
        bars_per_day = {
            '1m': 390,   # 1 minute: ~6.5 hours * 60 minutes
            '5m': 78,    # 5 minutes: ~6.5 hours * 12 bars per hour
            '15m': 26,   # 15 minutes: ~6.5 hours * 4 bars per hour
            '30m': 13,   # 30 minutes: ~6.5 hours * 2 bars per hour
            '1h': 6.5,   # 1 hour: ~6.5 trading hours per day
            '4h': 1.6,   # 4 hours: ~1.6 bars per day (6.5/4)
            '1d': 1,     # 1 day
            '1w': 0.2,   # 1 week: ~5 trading days per week
            '1m': 0.05   # 1 month: ~20 trading days per month
        }

        timeframe_lower = timeframe.lower()
        multiplier = bars_per_day.get(timeframe_lower, 1)
        bars = int(days * multiplier)

        # Ensure minimum and maximum limits
        bars = max(bars, 10)  # Minimum 10 bars
        bars = min(bars, 5000)  # Maximum 5000 bars (TradingView limit)

        return bars

    def _filter_data_by_days(self, data, days_requested, timeframe):
        """Filter data to get exactly the requested number of days"""
        try:
            from datetime import datetime, timedelta

            if data is None or data.empty:
                return data

            # For daily timeframe, simply take the last N days
            if timeframe.lower() == '1d':
                return data.tail(days_requested)

            # For intraday timeframes, calculate the date range
            # Get the last date in the data
            last_date = data.index[-1]

            # Calculate the start date (days_requested ago)
            if hasattr(last_date, 'date'):
                # If it's a datetime, get just the date part
                end_date = last_date.date()
            else:
                # If it's already a date, use it directly
                end_date = last_date

            start_date = end_date - timedelta(days=days_requested)

            # Filter data to include only the requested date range
            filtered_data = data[data.index.date >= start_date]

            return filtered_data

        except Exception as e:
            # If filtering fails, return the original data
            print(f"Warning: Could not filter data by days: {e}")
            return data

    def stop(self):
        self.is_running = False
        self.terminate()


class CandlestickChart(PlotWidget):
    """Candlestick chart widget using pyqtgraph - BLACK BACKGROUND with CROSSHAIR"""

    def __init__(self):
        super().__init__()
        # BLACK BACKGROUND as requested
        self.setBackground('black')
        self.setLabel('left', 'Price', color='white')
        self.setLabel('bottom', 'Time', color='white')
        self.showGrid(x=True, y=True, alpha=0.3)

        # Set axis colors to white for visibility on black background
        self.getAxis('left').setPen('white')
        self.getAxis('bottom').setPen('white')
        self.getAxis('left').setTextPen('white')
        self.getAxis('bottom').setTextPen('white')

        # Enable mouse interaction
        self.setMouseEnabled(x=True, y=True)
        self.enableAutoRange()

        # Initialize crosshair
        self.crosshair_enabled = True
        self.data_timestamps = None  # Store original timestamps for crosshair
        self.ohlc_data = None  # Store OHLC data for snapping
        self.setup_crosshair()

    def setup_crosshair(self):
        """Setup interactive crosshair with fixed legend"""
        # Create crosshair lines
        self.crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen(color='yellow', width=1, style=Qt.PenStyle.DashLine))
        self.crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen(color='yellow', width=1, style=Qt.PenStyle.DashLine))

        # Add crosshair to plot
        self.addItem(self.crosshair_v, ignoreBounds=True)
        self.addItem(self.crosshair_h, ignoreBounds=True)

        # Create fixed legend in top-left corner
        self.legend = pg.TextItem(
            text="Hover over chart for OHLC data",
            anchor=(0, 0),  # Top-left anchor
            color='white',
            fill=pg.mkBrush(0, 0, 0, 150),  # Semi-transparent black background
            border=pg.mkPen(color='gray', width=1)
        )
        self.addItem(self.legend, ignoreBounds=True)

        # Position legend in top-left corner
        self.position_legend()

        # Connect mouse move event
        self.scene().sigMouseMoved.connect(self.on_mouse_moved)

        # Connect view range change to reposition legend
        self.plotItem.vb.sigRangeChanged.connect(self.on_view_changed)

        # Initially hide crosshair
        self.hide_crosshair()

    def on_view_changed(self):
        """Handle view range changes to reposition legend"""
        if hasattr(self, 'legend'):
            self.position_legend()

    def position_legend(self):
        """Position the legend in the top-left corner"""
        try:
            # Get the view range
            view_range = self.plotItem.vb.viewRange()
            x_range, y_range = view_range

            # Position at top-left with small margin
            x_pos = x_range[0] + (x_range[1] - x_range[0]) * 0.02  # 2% from left
            y_pos = y_range[1] - (y_range[1] - y_range[0]) * 0.02  # 2% from top

            self.legend.setPos(x_pos, y_pos)
        except:
            # Fallback positioning
            self.legend.setPos(0, 0)

    def hide_crosshair(self):
        """Hide crosshair lines"""
        self.crosshair_v.hide()
        self.crosshair_h.hide()
        # Reset legend to default text
        self.legend.setText("Hover over chart for OHLC data")

    def show_crosshair(self):
        """Show crosshair lines"""
        if self.crosshair_enabled:
            self.crosshair_v.show()
            self.crosshair_h.show()
            # Legend stays visible with updated data

    def on_mouse_moved(self, pos):
        """Handle mouse movement for crosshair with OHLC snapping"""
        if not self.crosshair_enabled or self.data_timestamps is None or self.ohlc_data is None:
            return

        # Check if mouse is within the plot area
        if self.sceneBoundingRect().contains(pos):
            # Convert scene coordinates to data coordinates
            mouse_point = self.plotItem.vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()

            # Format coordinates for display
            try:
                # Get the closest candle index
                x_index = int(round(x))
                if 0 <= x_index < len(self.data_timestamps):
                    # Get OHLC values for this candle
                    candle_data = self.ohlc_data.iloc[x_index]
                    open_price = candle_data['open']
                    high_price = candle_data['high']
                    low_price = candle_data['low']
                    close_price = candle_data['close']

                    # Find the closest OHLC value to the mouse Y position
                    ohlc_values = [open_price, high_price, low_price, close_price]
                    ohlc_labels = ['Open', 'High', 'Low', 'Close']

                    # Calculate distances to each OHLC value
                    distances = [abs(y - price) for price in ohlc_values]
                    closest_index = distances.index(min(distances))

                    # Snap to the closest OHLC value
                    snap_price = ohlc_values[closest_index]
                    snap_label = ohlc_labels[closest_index]

                    # Update crosshair position (snap X to candle center, Y to OHLC value)
                    self.crosshair_v.setPos(x_index)  # Snap to candle center
                    self.crosshair_h.setPos(snap_price)  # Snap to OHLC value

                    # Get timestamp
                    timestamp = self.data_timestamps[x_index]
                    if hasattr(timestamp, 'strftime'):
                        time_str = timestamp.strftime("%Y-%m-%d %H:%M")
                    else:
                        time_str = str(timestamp)

                    # Update legend with OHLC info
                    legend_text = (
                        f"{time_str}\n"
                        f"{snap_label}: ${snap_price:.2f}\n"
                        f"────────────────────\n"
                        f"Open:  ${open_price:.2f}\n"
                        f"High:  ${high_price:.2f}\n"
                        f"Low:   ${low_price:.2f}\n"
                        f"Close: ${close_price:.2f}"
                    )
                    self.legend.setText(legend_text)

                    # Show crosshair
                    self.show_crosshair()
                else:
                    self.hide_crosshair()
            except (IndexError, ValueError, TypeError):
                self.hide_crosshair()
        else:
            self.hide_crosshair()

    def plot_candlesticks(self, data: pd.DataFrame, symbol: str):
        """Plot proper candlestick data - OPTIMIZED for performance with CROSSHAIR support"""
        try:
            self.clear()

            if data is None or data.empty:
                self.hide_crosshair()
                return

            # Store original timestamps and OHLC data for crosshair
            self.data_timestamps = data.index
            self.ohlc_data = data[['open', 'high', 'low', 'close']].copy()

            # Prepare data efficiently
            timestamps = np.arange(len(data))
            opens = data['open'].values
            highs = data['high'].values
            lows = data['low'].values
            closes = data['close'].values

            # Calculate candle width based on data density
            candle_width = 0.6 if len(data) < 100 else 0.8

            # STEP 1: Plot all wicks efficiently
            wick_x = []
            wick_y = []
            for i in range(len(timestamps)):
                wick_x.extend([timestamps[i], timestamps[i], np.nan])
                wick_y.extend([lows[i], highs[i], np.nan])

            self.plot(wick_x, wick_y, pen=mkPen(color='gray', width=1))

            # STEP 2: Plot candle bodies using BarGraphItem (much more efficient)
            from pyqtgraph import BarGraphItem

            # Prepare data for bar graph
            green_mask = closes >= opens
            red_mask = closes < opens

            # Green candles (bullish)
            if np.any(green_mask):
                green_x = timestamps[green_mask]
                green_heights = np.abs(closes[green_mask] - opens[green_mask])
                green_bottoms = np.minimum(opens[green_mask], closes[green_mask])

                # Create green bars
                green_bars = BarGraphItem(
                    x=green_x,
                    height=green_heights,
                    width=candle_width,
                    y0=green_bottoms,
                    brush=pg.mkBrush(color='g'),
                    pen=pg.mkPen(color='g', width=1)
                )
                self.addItem(green_bars)

            # Red candles (bearish)
            if np.any(red_mask):
                red_x = timestamps[red_mask]
                red_heights = np.abs(closes[red_mask] - opens[red_mask])
                red_bottoms = np.minimum(opens[red_mask], closes[red_mask])

                # Create red bars
                red_bars = BarGraphItem(
                    x=red_x,
                    height=red_heights,
                    width=candle_width,
                    y0=red_bottoms,
                    brush=pg.mkBrush(color='r'),
                    pen=pg.mkPen(color='r', width=1)
                )
                self.addItem(red_bars)

            # Re-add crosshair and legend after clearing (since clear() removes all items)
            self.addItem(self.crosshair_v, ignoreBounds=True)
            self.addItem(self.crosshair_h, ignoreBounds=True)
            self.addItem(self.legend, ignoreBounds=True)

            # Set title and auto-range
            self.setTitle(f"{symbol} - Candlestick Chart ({len(data)} bars)", color='white')
            self.autoRange()

            # Position legend after auto-range
            self.position_legend()

            # Enable crosshair now that we have data
            self.crosshair_enabled = True

        except Exception as e:
            print(f"Error plotting candlesticks: {e}")
            self.hide_crosshair()


class TradingViewScraperTab(QWidget):
    """TradingView scraper as a tab widget for integration into main application"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_data = None
        self.worker = None
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface for the tab"""
        # Main layout
        main_layout = QHBoxLayout(self)

        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - Controls
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)

        # Right panel - Chart and data
        right_panel = self.create_chart_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions (30% left, 70% right)
        splitter.setSizes([400, 1000])

    def create_control_panel(self):
        """Create the left control panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Title
        title = QLabel("TradingView Data Scraper")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Input group
        input_group = QGroupBox("Data Input")
        input_layout = QGridLayout(input_group)

        # Symbol input
        input_layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_input = QLineEdit("MES1!")
        self.symbol_input.setPlaceholderText("e.g., MES1!, MESU2025, ES1!, ESU2025, AAPL, BTCUSD")
        # Connect to auto-detect exchange when symbol changes (with delay)
        self.symbol_input.textChanged.connect(self.on_symbol_changed)
        input_layout.addWidget(self.symbol_input, 0, 1)

        # Timer for delayed auto-detection (wait 1 second after user stops typing)
        self.auto_detect_timer = QTimer()
        self.auto_detect_timer.setSingleShot(True)  # Only fire once
        self.auto_detect_timer.timeout.connect(self.perform_auto_detection)
        self.auto_detect_timer.setInterval(1000)  # 1 second delay

        # Exchange dropdown with auto-detect
        input_layout.addWidget(QLabel("Exchange:"), 1, 0)
        self.exchange_combo = QComboBox()
        # Add exchanges in order of importance (mini/micro contracts first, then major US exchanges)
        exchanges = list(Config.EXCHANGES.keys())
        self.exchange_combo.addItems(exchanges)
        # Set first exchange (CME_MINI - includes both mini and micro contracts) as default
        self.exchange_combo.setCurrentText("CME_MINI")
        input_layout.addWidget(self.exchange_combo, 1, 1)

        # Timeframe dropdown
        input_layout.addWidget(QLabel("Timeframe:"), 2, 0)
        self.timeframe_combo = QComboBox()
        timeframes = ["1m", "5m", "15m", "30m", "1h", "4h", "1D", "1W", "1M"]
        self.timeframe_combo.addItems(timeframes)
        self.timeframe_combo.setCurrentText("1D")
        input_layout.addWidget(self.timeframe_combo, 2, 1)

        # Days to load input (NO LIMIT)
        input_layout.addWidget(QLabel("Days to Load:"), 3, 0)
        self.days_input = QSpinBox()
        self.days_input.setMinimum(1)
        self.days_input.setMaximum(99999)  # Effectively unlimited
        self.days_input.setValue(30)
        self.days_input.setSpecialValueText("Unlimited")
        input_layout.addWidget(self.days_input, 3, 1)

        layout.addWidget(input_group)

        # Contract Info group
        info_group = QGroupBox("Futures Contract Info")
        info_layout = QVBoxLayout(info_group)

        info_text = QLabel(
            "Contract Month Codes:\n"
            "H=Mar, M=Jun, U=Sep, Z=Dec\n"
            "F=Jan, G=Feb, J=Apr, K=May\n"
            "N=Jul, Q=Aug, V=Oct, X=Nov\n\n"
            "Examples:\n"
            "• MES1! = Micro S&P Continuous\n"
            "• MESU2025 = Micro S&P Sep 2025\n"
            "• ESZ2025 = E-mini S&P Dec 2025"
        )
        info_text.setStyleSheet("font-size: 10px; color: #666;")
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)

        layout.addWidget(info_group)

        # Credentials group (optional)
        cred_group = QGroupBox("Credentials (Optional)")
        cred_layout = QGridLayout(cred_group)

        cred_layout.addWidget(QLabel("Username:"), 0, 0)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("TradingView username")
        cred_layout.addWidget(self.username_input, 0, 1)

        cred_layout.addWidget(QLabel("Password:"), 1, 0)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("TradingView password")
        cred_layout.addWidget(self.password_input, 1, 1)

        layout.addWidget(cred_group)

        # Fetch button
        self.fetch_button = QPushButton("Fetch Data")
        self.fetch_button.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.fetch_button.setStyleSheet(self.get_button_style())
        self.fetch_button.clicked.connect(self.fetch_data)
        layout.addWidget(self.fetch_button)

        # Export group
        export_group = QGroupBox("Export Data")
        export_layout = QVBoxLayout(export_group)

        export_buttons_layout = QHBoxLayout()

        self.export_csv_button = QPushButton("Export CSV & JSON")
        self.export_csv_button.setStyleSheet(self.get_button_style())
        self.export_csv_button.clicked.connect(self.export_both_formats)
        self.export_csv_button.setEnabled(False)
        export_buttons_layout.addWidget(self.export_csv_button)

        export_layout.addLayout(export_buttons_layout)
        layout.addWidget(export_group)

        # Auto-detection settings
        auto_group = QGroupBox("Auto-Detection Settings")
        auto_layout = QVBoxLayout(auto_group)

        self.auto_detect_checkbox = QCheckBox("Enable automatic exchange detection (1s delay)")
        self.auto_detect_checkbox.setChecked(True)  # Enabled by default
        self.auto_detect_checkbox.setToolTip("Automatically detect and select exchange 1 second after you stop typing")
        auto_layout.addWidget(self.auto_detect_checkbox)

        layout.addWidget(auto_group)

        # Chart controls
        chart_group = QGroupBox("Chart Controls")
        chart_layout = QVBoxLayout(chart_group)

        self.crosshair_button = QPushButton("Crosshair: ON")
        self.crosshair_button.setStyleSheet(self.get_button_style())
        self.crosshair_button.clicked.connect(self.toggle_crosshair)
        self.crosshair_button.setCheckable(True)
        self.crosshair_button.setChecked(True)  # Crosshair enabled by default
        chart_layout.addWidget(self.crosshair_button)

        layout.addWidget(chart_group)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Status text
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)

        # Add stretch to push everything to top
        layout.addStretch()

        return panel

    def create_chart_panel(self):
        """Create the right chart and data panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Chart title
        self.chart_title = QLabel("Chart and Data will appear here after fetching data")
        self.chart_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.chart_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.chart_title)

        # Create splitter for chart and data table
        chart_data_splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(chart_data_splitter)

        # Chart widget
        self.chart_widget = CandlestickChart()
        chart_data_splitter.addWidget(self.chart_widget)

        # Data table widget
        self.data_table = self.create_data_table()
        chart_data_splitter.addWidget(self.data_table)

        # Set splitter proportions (70% chart, 30% table)
        chart_data_splitter.setSizes([700, 300])

        return panel

    def create_data_table(self):
        """Create the data table widget"""
        table = QTableWidget()

        # Set table properties
        table.setAlternatingRowColors(False)  # Remove black/red alternating pattern
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        table.setSortingEnabled(True)

        # Set headers
        headers = ['Date/Time', 'Open', 'High', 'Low', 'Close', 'Volume']
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)

        # Configure header
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Date column

        # Set initial message
        table.setRowCount(1)
        table.setItem(0, 0, QTableWidgetItem("No data loaded"))
        for i in range(1, len(headers)):
            table.setItem(0, i, QTableWidgetItem(""))

        return table

    def get_button_style(self):
        """Get consistent button styling."""
        return """
            QPushButton {
                background-color: white;
                color: black;
                border: 1px solid black;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #808080;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #9E9E9E;
                color: #3e3e3e;
            }
        """

    def fetch_data(self):
        """Fetch data in a separate thread"""
        if self.worker and self.worker.isRunning():
            return

        # Get input values
        symbol = self.symbol_input.text().strip()
        exchange = self.exchange_combo.currentText()
        timeframe = self.timeframe_combo.currentText()
        days_to_load = self.days_input.value()
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not symbol:
            QMessageBox.warning(self, "Warning", "Please enter a symbol")
            return

        # Disable fetch button and show progress
        self.fetch_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # Clear previous status
        self.status_text.clear()

        # Get contract information for display
        contract_info = Config.get_contract_info(symbol)
        self.log_message(f"Starting data fetch for {contract_info} from {exchange}...")

        # Create and start worker thread
        self.worker = DataFetchWorker(symbol, exchange, timeframe, days_to_load, username, password)
        self.worker.progress.connect(self.log_message)
        self.worker.data_ready.connect(self.on_data_ready)
        self.worker.error.connect(self.on_error)
        self.worker.finished.connect(self.on_fetch_finished)
        self.worker.start()

    def on_data_ready(self, data, symbol, exchange, timeframe):
        """Handle successful data fetch"""
        self.current_data = data

        # Update chart
        self.chart_widget.plot_candlesticks(data, symbol)
        contract_info = Config.get_contract_info(symbol)
        self.chart_title.setText(f"{contract_info} ({exchange}) - {timeframe} - {len(data)} bars")

        # Update data table
        self.populate_data_table(data)

        # Enable export button
        self.export_csv_button.setEnabled(True)

        # Log success
        self.log_message(f"✓ Successfully loaded {len(data)} bars")
        self.log_message(f"Date range: {data.index[0]} to {data.index[-1]}")
        self.log_message(f"Latest close: ${data['close'].iloc[-1]:.2f}")

    def on_error(self, error_message):
        """Handle fetch error"""
        self.log_message(f"✗ Error: {error_message}")
        QMessageBox.critical(self, "Error", f"Failed to fetch data:\n{error_message}")

    def on_fetch_finished(self):
        """Handle fetch completion"""
        self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.worker = None

    def toggle_crosshair(self):
        """Toggle crosshair on/off"""
        self.chart_widget.crosshair_enabled = self.crosshair_button.isChecked()

        if self.chart_widget.crosshair_enabled:
            self.crosshair_button.setText("Crosshair: ON")
            if self.chart_widget.data_timestamps is not None:
                self.chart_widget.show_crosshair()
        else:
            self.crosshair_button.setText("Crosshair: OFF")
            self.chart_widget.hide_crosshair()

    def export_both_formats(self):
        """Export current data to both CSV and JSON formats"""
        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "No data to export")
            return

        try:
            symbol = self.symbol_input.text().strip()
            exchange = self.exchange_combo.currentText()
            timeframe = self.timeframe_combo.currentText()

            # Create scraper instance for export functionality
            scraper = TradingViewScraper()

            # Export to CSV
            csv_filepath = scraper.export_data(self.current_data, symbol, exchange, timeframe, "csv")

            # Export to JSON
            json_filepath = scraper.export_data(self.current_data, symbol, exchange, timeframe, "json")

            self.log_message(f"✓ Data exported to CSV: {csv_filepath}")
            self.log_message(f"✓ Data exported to JSON: {json_filepath}")

            QMessageBox.information(self, "Export Successful",
                                   f"Data exported to:\nCSV: {csv_filepath}\nJSON: {json_filepath}")

        except Exception as e:
            error_msg = f"Export failed: {str(e)}"
            self.log_message(f"✗ {error_msg}")
            QMessageBox.critical(self, "Export Error", error_msg)

    def export_data(self, format_type):
        """Export current data to file (legacy method)"""
        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "No data to export")
            return

        try:
            symbol = self.symbol_input.text().strip()
            exchange = self.exchange_combo.currentText()
            timeframe = self.timeframe_combo.currentText()

            # Create scraper instance for export functionality
            scraper = TradingViewScraper()
            filepath = scraper.export_data(self.current_data, symbol, exchange, timeframe, format_type)

            self.log_message(f"✓ Data exported to: {filepath}")
            QMessageBox.information(self, "Export Successful", f"Data exported to:\n{filepath}")

        except Exception as e:
            error_msg = f"Export failed: {str(e)}"
            self.log_message(f"✗ {error_msg}")
            QMessageBox.critical(self, "Export Error", error_msg)

    def populate_data_table(self, data):
        """Populate the data table with OHLCV data"""
        try:
            if data is None or data.empty:
                return

            # Clear existing data
            self.data_table.setRowCount(0)

            # Set row count
            self.data_table.setRowCount(len(data))

            # Populate table with data (oldest first - chronological order)
            # No reversal - show data in natural chronological order

            for row, (timestamp, row_data) in enumerate(data.iterrows()):
                # Format timestamp
                if hasattr(timestamp, 'strftime'):
                    date_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    date_str = str(timestamp)

                # Add data to table
                self.data_table.setItem(row, 0, QTableWidgetItem(date_str))
                self.data_table.setItem(row, 1, QTableWidgetItem(f"{row_data['open']:.2f}"))
                self.data_table.setItem(row, 2, QTableWidgetItem(f"{row_data['high']:.2f}"))
                self.data_table.setItem(row, 3, QTableWidgetItem(f"{row_data['low']:.2f}"))
                self.data_table.setItem(row, 4, QTableWidgetItem(f"{row_data['close']:.2f}"))

                # Format volume (handle large numbers)
                volume = row_data['volume']
                if volume >= 1_000_000:
                    volume_str = f"{volume/1_000_000:.1f}M"
                elif volume >= 1_000:
                    volume_str = f"{volume/1_000:.1f}K"
                else:
                    volume_str = f"{volume:.0f}"

                self.data_table.setItem(row, 5, QTableWidgetItem(volume_str))

            # Resize columns to content
            self.data_table.resizeColumnsToContents()

        except Exception as e:
            self.log_message(f"Error populating data table: {e}")

    def on_symbol_changed(self):
        """Handle symbol input changes - start timer for delayed auto-detection"""
        # Check if automatic detection is enabled
        if not self.auto_detect_checkbox.isChecked():
            return

        # Stop any existing timer
        self.auto_detect_timer.stop()

        # Start timer if symbol has any characters (including 1-letter tickers like F, T)
        symbol = self.symbol_input.text().strip()
        if symbol and len(symbol) >= 1:
            # Start/restart the timer - will trigger auto-detection after 1 second of no typing
            self.auto_detect_timer.start()

    def perform_auto_detection(self):
        """Perform the actual auto-detection after user stops typing"""
        symbol = self.symbol_input.text().strip()

        # Double-check that auto-detection is still enabled and symbol is valid
        if not self.auto_detect_checkbox.isChecked() or not symbol or len(symbol) < 1:
            return

        # Get auto-detection info
        exchange_info = Config.get_auto_exchange_info(symbol)
        detected_exchange = exchange_info['detected_exchange']
        confidence = exchange_info['confidence']
        reason = exchange_info['reason']

        # Auto-change for ALL confidence levels (high, medium, low)
        # Set the detected exchange in the combo box
        if detected_exchange in [self.exchange_combo.itemText(i) for i in range(self.exchange_combo.count())]:
            current_exchange = self.exchange_combo.currentText()
            if current_exchange != detected_exchange:
                self.exchange_combo.setCurrentText(detected_exchange)

                # Log the automatic detection with appropriate emoji
                confidence_emoji = {"high": "🎯", "medium": "✓", "low": "⚠️"}
                emoji = confidence_emoji.get(confidence, "ℹ️")
                self.log_message(f"{emoji} Auto-selected {detected_exchange} for {symbol} ({confidence} confidence)")



    def log_message(self, message):
        """Add message to status text"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")

        # Auto-scroll to bottom
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


class TradingViewScraperGUI(QMainWindow):
    """Main GUI window for TradingView data scraper"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("TradingView Data Scraper")
        self.setGeometry(100, 100, 1400, 900)

        # Create the tab widget as central widget
        self.scraper_tab = TradingViewScraperTab()
        self.setCentralWidget(self.scraper_tab)




# ============================================================================
# MAIN APPLICATION
# ============================================================================

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("TradingView Data Scraper")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = TradingViewScraperGUI()
    window.show()

    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
