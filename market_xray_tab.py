"""
Options Data Scraper Tab

This module provides a tab for collecting and displaying end-of-day options data.
It allows users to fetch options chains for specific symbols and dates, and view
the data in a structured format.
"""

import logging
import pandas as pd
from datetime import datetime, date, timedelta
from PyQt6 import QtWidgets, QtCore, QtGui
from PyQt6.QtCore import Qt

# Import theme colors and create greyscale version for Options Data tab
try:
    import theme
    BASE_THEME = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    BASE_THEME = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
        'neutral': '#9E9E9E',              # Material Design Grey
        'highlight': '#FFC107',            # Material Design Amber
    }

# Create greyscale theme colors for Options Data tab (no blue colors)
THEME_COLORS = BASE_THEME.copy()
THEME_COLORS.update({
    'primary_accent': '#6e6e6e',       # Medium gray instead of blue
    'secondary_accent': '#8e8e8e',     # Lighter gray instead of blue
    'pressed_accent': '#4e4e4e',       # Darker gray instead of blue
    'loading_accent': '#6e6e6e',       # Medium gray instead of blue
    'button_shadow': '0 4px 6px rgba(110, 110, 110, 0.3)',  # Gray shadow instead of blue
})

# Import options data functionality
try:
    from combined_options_analysis import fetch_options_for_date, get_current_price
    OPTIONS_AVAILABLE = True
except ImportError:
    OPTIONS_AVAILABLE = False

logger = logging.getLogger("MarketXRAY")

class OptionsChainWindow(QtWidgets.QDialog):
    """
    Popup window to display the full options chain data.
    """
    def __init__(self, options_data, symbol, date_str, parent=None):
        super().__init__(parent)
        self.options_data = options_data
        self.symbol = symbol
        self.date_str = date_str

        self.setWindowTitle(f"Options Chain - {symbol} ({date_str})")
        self.setModal(False)  # Allow interaction with main window
        self.resize(1000, 600)

        self.init_ui()

    def init_ui(self):
        """Initialize the options chain window UI."""
        layout = QtWidgets.QVBoxLayout(self)

        # Header info
        header_label = QtWidgets.QLabel(f"Options Chain for {self.symbol} - {self.date_str}")
        header_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {THEME_COLORS['text']};
                padding: 10px;
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
            }}
        """)
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header_label)

        # Options table
        self.options_table = QtWidgets.QTableView()
        self.options_model = OptionsTableModel(self.options_data, "unified")
        self.options_table.setModel(self.options_model)
        self.setup_table_view(self.options_table)
        layout.addWidget(self.options_table)

        # Button bar
        button_layout = QtWidgets.QHBoxLayout()

        # Export button
        export_btn = QtWidgets.QPushButton("Export CSV")
        export_btn.clicked.connect(self.export_data)
        export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 8px 16px;
                color: {THEME_COLORS['text']};
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
            }}
        """)
        button_layout.addWidget(export_btn)

        button_layout.addStretch()

        # Close button
        close_btn = QtWidgets.QPushButton("Close")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['primary_accent']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 8px 16px;
                color: {THEME_COLORS['text']};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['secondary_accent']};
            }}
        """)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        # Apply theme
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
            }}
        """)

    def setup_table_view(self, table_view):
        """Configure the table view."""
        table_view.setAlternatingRowColors(True)
        table_view.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        table_view.setSelectionMode(QtWidgets.QAbstractItemView.SelectionMode.SingleSelection)
        table_view.setSortingEnabled(True)
        table_view.verticalHeader().setVisible(False)
        table_view.horizontalHeader().setStretchLastSection(True)

        # Style the table
        table_view.setStyleSheet(f"""
            QTableView {{
                background-color: {THEME_COLORS['background']};
                alternate-background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                gridline-color: {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
                selection-color: {THEME_COLORS['text']};
            }}
            QHeaderView::section {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                padding: 5px;
                border: 1px solid {THEME_COLORS['borders']};
                font-weight: bold;
            }}
        """)

        # Resize columns to content
        table_view.resizeColumnsToContents()

    def export_data(self):
        """Export the options data to CSV."""
        if self.options_data.empty:
            return

        file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "Export Options Data",
            f"{self.symbol}_options_{self.date_str.replace('-', '')}.csv",
            "CSV Files (*.csv)"
        )

        if file_path:
            try:
                self.options_data.to_csv(file_path, index=False)
                # Show success message briefly
                QtWidgets.QMessageBox.information(self, "Export Successful", f"Data exported to {file_path}")
            except Exception as e:
                QtWidgets.QMessageBox.warning(self, "Export Error", f"Error exporting data: {str(e)}")


class OptionsTableModel(QtCore.QAbstractTableModel):
    """
    Model for displaying options data in a table.
    """
    def __init__(self, data=None, option_type="calls"):
        super().__init__()
        self._data = data if data is not None else pd.DataFrame()
        self.option_type = option_type
        self._columns = []
        if not self._data.empty:
            self._columns = self._data.columns.tolist()

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._columns)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or self._data.empty:
            return None

        row = index.row()
        col = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            value = self._data.iloc[row, col]
            if pd.isna(value):
                return ""
            
            # Format numeric values appropriately
            if isinstance(value, (int, float)):
                if self._columns[col] in ['strike', 'lastPrice', 'bid', 'ask']:
                    return f"{value:.2f}"
                elif self._columns[col] in ['volume', 'openInterest']:
                    return f"{int(value)}" if not pd.isna(value) else ""
                elif self._columns[col] in ['dollar_volume', 'dollar_oi']:
                    # Format dollar values with appropriate scaling
                    if value >= 1_000_000:
                        return f"${value/1_000_000:.1f}M"
                    elif value >= 1_000:
                        return f"${value/1_000:.1f}K"
                    else:
                        return f"${value:.0f}"
                elif self._columns[col] in ['impliedVolatility']:
                    return f"{value:.4f}" if not pd.isna(value) else ""
                else:
                    return str(value)
            return str(value)

        elif role == Qt.ItemDataRole.BackgroundRole:
            # Color coding based on option type for unified table
            if self.option_type == "unified" and not self._data.empty:
                # Check if this row is a call or put
                if 'option_type' in self._data.columns:
                    option_type = self._data.iloc[row]['option_type']
                    if option_type == 'call':
                        return QtGui.QColor(THEME_COLORS['control_panel']).lighter(110)
                    elif option_type == 'put':
                        return QtGui.QColor(THEME_COLORS['control_panel']).lighter(105)
            elif self.option_type == "calls":
                return QtGui.QColor(THEME_COLORS['control_panel']).lighter(110)
            else:
                return QtGui.QColor(THEME_COLORS['control_panel']).lighter(105)

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Right-align numeric columns
            if self._columns[index.column()] in ['strike', 'lastPrice', 'bid', 'ask', 'volume', 'openInterest', 'dollar_volume', 'dollar_oi', 'impliedVolatility']:
                return Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter
            return Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole:
            if orientation == Qt.Orientation.Horizontal:
                if section < len(self._columns):
                    return self._columns[section]
            else:
                return str(section + 1)
        return None

    def setData(self, data, option_type="calls"):
        """Update the model with new data"""
        self.beginResetModel()
        self._data = data if data is not None else pd.DataFrame()
        self.option_type = option_type
        if not self._data.empty:
            self._columns = self._data.columns.tolist()
        else:
            self._columns = []
        self.endResetModel()
        return True


class OptionsDataTab(QtWidgets.QWidget):
    """
    Tab for collecting and displaying end-of-day options data.
    """
    def __init__(self, parent=None):
        """
        Initialize the Options Data Scraper tab.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.parent = parent

        # Initialize data storage
        self.options_data = pd.DataFrame()
        self.current_symbol = ""
        self.current_price = 0.0
        self.current_date_str = ""

        # Keep reference to options chain window
        self.options_chain_window = None

        # Initialize UI
        self.init_ui()

        # Set up timer for periodic updates (optional)
        self.update_timer = QtCore.QTimer()
        self.update_timer.timeout.connect(self.refresh_data)

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Control panel (fixed size)
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # Data summary area (20% of remaining space)
        summary_area = self.create_summary_area()
        main_layout.addWidget(summary_area, 1)  # Stretch factor 1

        # Options data table area (80% of remaining space)
        options_data_area = self.create_data_area()
        main_layout.addWidget(options_data_area, 4)  # Stretch factor 4 (4:1 ratio = 80:20)

        # Status bar (fixed size)
        self.status_label = QtWidgets.QLabel("Ready to fetch END OF DAY options data")
        self.status_label.setStyleSheet(f"color: {THEME_COLORS['text']}; padding: 5px;")
        main_layout.addWidget(self.status_label)

        # Apply theme
        self.apply_theme()

    def create_control_panel(self):
        """Create the control panel with input fields and buttons."""
        panel = QtWidgets.QGroupBox("END OF DAY Options Data Collection")
        panel.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: transparent;
                color: {THEME_COLORS['text']};
            }}
        """)

        layout = QtWidgets.QHBoxLayout(panel)
        layout.setSpacing(15)

        # Symbol input
        symbol_label = QtWidgets.QLabel("Symbol:")
        symbol_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        layout.addWidget(symbol_label)

        self.symbol_input = QtWidgets.QLineEdit()
        self.symbol_input.setPlaceholderText("e.g., AAPL, SPY")
        self.symbol_input.setFixedWidth(100)
        self.symbol_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px;
                color: {THEME_COLORS['text']};
            }}
            QLineEdit:hover {{
                border: 1px solid {THEME_COLORS['text']};
            }}
            QLineEdit:focus {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
        """)
        layout.addWidget(self.symbol_input)

        # Date picker for END OF DAY data only
        date_label = QtWidgets.QLabel("EOD Date:")
        date_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        date_label.setToolTip("End of Day data only - no intraday data")
        layout.addWidget(date_label)

        self.date_picker = QtWidgets.QDateEdit()
        # Set to previous trading day by default (not today to ensure EOD data)
        self.date_picker.setDate(self.get_previous_trading_day())
        self.date_picker.setCalendarPopup(True)
        # Set maximum date to yesterday to prevent future/today selection
        self.date_picker.setMaximumDate(self.get_previous_trading_day())
        self.date_picker.setStyleSheet(f"""
            QDateEdit {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px;
                color: {THEME_COLORS['text']};
            }}
            QDateEdit:hover {{
                border: 1px solid {THEME_COLORS['text']};
            }}
            QDateEdit:focus {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
        """)
        layout.addWidget(self.date_picker)

        # Expiration date picker
        exp_label = QtWidgets.QLabel("Expiration:")
        exp_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        layout.addWidget(exp_label)

        self.expiration_combo = QtWidgets.QComboBox()
        self.expiration_combo.setFixedWidth(120)
        self.expiration_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px;
                color: {THEME_COLORS['text']};
            }}
            QComboBox:hover {{
                border: 1px solid {THEME_COLORS['text']};
            }}
            QComboBox:focus {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 15px;
                border-left-width: 1px;
                border-left-color: {THEME_COLORS['borders']};
                border-left-style: solid;
            }}
        """)
        layout.addWidget(self.expiration_combo)

        # Populate expiration dates (next few Fridays)
        self.populate_expiration_dates()

        # Fetch button
        self.fetch_button = QtWidgets.QPushButton("Fetch EOD Data")
        self.fetch_button.setFixedWidth(150)
        self.fetch_button.clicked.connect(self.fetch_options_data)
        self.fetch_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['primary_accent']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                color: {THEME_COLORS['text']};
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['secondary_accent']};
                border: 1px solid {THEME_COLORS['text']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)
        layout.addWidget(self.fetch_button)

        # Auto-refresh checkbox (disabled for EOD data)
        self.auto_refresh_check = QtWidgets.QCheckBox("Auto-refresh (EOD only)")
        self.auto_refresh_check.setStyleSheet(f"color: {THEME_COLORS['text']};")
        self.auto_refresh_check.setEnabled(False)  # Disabled for EOD data
        self.auto_refresh_check.setToolTip("Auto-refresh disabled for END OF DAY data collection")
        self.auto_refresh_check.stateChanged.connect(self.toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_check)



        # Export button
        self.export_button = QtWidgets.QPushButton("Export CSV")
        self.export_button.setFixedWidth(100)
        self.export_button.clicked.connect(self.export_data)
        self.export_button.setEnabled(False)  # Disabled until data is fetched
        self.export_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 6px 12px;
                color: {THEME_COLORS['text']};
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid {THEME_COLORS['text']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['background']};
            }}
            QPushButton:disabled {{
                background-color: {THEME_COLORS['borders']};
                color: {THEME_COLORS['neutral']};
            }}
        """)
        layout.addWidget(self.export_button)

        # Add stretch to push everything to the left
        layout.addStretch()

        return panel

    def create_summary_area(self):
        """Create a summary area showing basic options data info."""
        # Create a group box for the summary
        summary_group = QtWidgets.QGroupBox("Options Data Summary")
        summary_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: transparent;
                color: {THEME_COLORS['text']};
            }}
        """)

        summary_layout = QtWidgets.QVBoxLayout(summary_group)

        # Create summary labels
        self.summary_info = QtWidgets.QLabel("No options data loaded")
        self.summary_info.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS['text']};
                font-size: 14px;
                padding: 20px;
                background-color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
            }}
        """)
        self.summary_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        summary_layout.addWidget(self.summary_info)

        # Add some spacing
        summary_layout.addStretch()

        return summary_group

    def create_data_area(self):
        """Create the data display area with a single unified table for options."""
        # Create a single group box for all options data
        options_group = QtWidgets.QGroupBox("Options Chain")
        options_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: transparent;
                color: {THEME_COLORS['text']};
            }}
        """)

        options_layout = QtWidgets.QVBoxLayout(options_group)

        # Create single unified table
        self.options_table = QtWidgets.QTableView()
        self.options_model = OptionsTableModel(option_type="unified")
        self.options_table.setModel(self.options_model)
        self.setup_table_view(self.options_table)
        options_layout.addWidget(self.options_table)

        return options_group

    def setup_table_view(self, table_view):
        """Configure a table view with appropriate settings."""
        table_view.setAlternatingRowColors(True)
        table_view.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        table_view.setSelectionMode(QtWidgets.QAbstractItemView.SelectionMode.SingleSelection)
        table_view.setSortingEnabled(True)
        table_view.verticalHeader().setVisible(False)
        table_view.horizontalHeader().setStretchLastSection(True)

        # Style the table
        table_view.setStyleSheet(f"""
            QTableView {{
                background-color: {THEME_COLORS['background']};
                alternate-background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                gridline-color: {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
                selection-color: {THEME_COLORS['text']};
            }}
            QHeaderView::section {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                padding: 5px;
                border: 1px solid {THEME_COLORS['borders']};
                font-weight: bold;
            }}
        """)

    def calculate_dollar_values(self, df):
        """Calculate dollar volume and dollar open interest for options data.

        Formulas:
        $Volume = Volume × Option_Premium × Contract_Multiplier (100)
        $OI = Open_Interest × Option_Premium × Contract_Multiplier (100)
        """
        if df.empty:
            return df

        df_copy = df.copy()

        # Calculate dollar volume: Volume × Option_Premium × Contract_Multiplier
        # Using lastPrice as the Option_Premium and 100 as Contract_Multiplier
        if 'volume' in df_copy.columns and 'lastPrice' in df_copy.columns:
            df_copy['dollar_volume'] = df_copy['volume'] * df_copy['lastPrice'] * 100
            # Handle NaN values
            df_copy['dollar_volume'] = df_copy['dollar_volume'].fillna(0)

        # Calculate dollar open interest: Open_Interest × Option_Premium × Contract_Multiplier
        if 'openInterest' in df_copy.columns and 'lastPrice' in df_copy.columns:
            df_copy['dollar_oi'] = df_copy['openInterest'] * df_copy['lastPrice'] * 100
            # Handle NaN values
            df_copy['dollar_oi'] = df_copy['dollar_oi'].fillna(0)

        return df_copy



    def get_previous_trading_day(self):
        """Get the previous trading day (excluding weekends) for EOD data."""
        current_date = datetime.now().date()

        # Go back one day
        previous_date = current_date - timedelta(days=1)

        # If it's a weekend, go back to Friday
        while previous_date.weekday() > 4:  # Monday=0, Sunday=6
            previous_date = previous_date - timedelta(days=1)

        return QtCore.QDate(previous_date)

    def is_end_of_day_time(self):
        """Check if current time is after market close (4 PM ET) for EOD data validation."""
        from datetime import datetime
        import pytz

        try:
            # Get current time in Eastern Time
            et_tz = pytz.timezone('US/Eastern')
            current_et = datetime.now(et_tz)

            # Market closes at 4 PM ET
            market_close = current_et.replace(hour=16, minute=0, second=0, microsecond=0)

            return current_et >= market_close
        except:
            # If timezone handling fails, assume it's after market close
            return True

    def populate_expiration_dates(self):
        """Populate the expiration combo box with upcoming option expiration dates."""
        self.expiration_combo.clear()

        # Get next 8 Fridays (typical option expiration dates)
        current_date = datetime.now().date()
        fridays = []

        # Find next Friday
        days_ahead = 4 - current_date.weekday()  # Friday is weekday 4
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7

        next_friday = current_date + timedelta(days=days_ahead)

        # Add next 8 Fridays
        for i in range(8):
            friday = next_friday + timedelta(weeks=i)
            fridays.append(friday)
            self.expiration_combo.addItem(friday.strftime("%Y-%m-%d"), friday.strftime("%Y-%m-%d"))

    def fetch_options_data(self):
        """Fetch END OF DAY options data for the specified symbol and expiration date."""
        if not OPTIONS_AVAILABLE:
            self.update_status("Options functionality not available", "error")
            return

        symbol = self.symbol_input.text().strip().upper()
        if not symbol:
            self.update_status("Please enter a symbol", "error")
            return

        expiration_date = self.expiration_combo.currentData()
        if not expiration_date:
            self.update_status("Please select an expiration date", "error")
            return

        # Validate that we're collecting END OF DAY data only
        qdate = self.date_picker.date()
        selected_date = date(qdate.year(), qdate.month(), qdate.day())
        today = datetime.now().date()

        # Prevent collecting data for today unless it's after market close
        if selected_date >= today:
            if selected_date > today:
                self.update_status("Cannot collect future data - END OF DAY data only", "error")
                return
            elif not self.is_end_of_day_time():
                self.update_status("Cannot collect today's data before market close (4 PM ET) - END OF DAY data only", "error")
                return

        # Prevent weekend data collection
        if selected_date.weekday() > 4:  # Saturday=5, Sunday=6
            self.update_status("Cannot collect weekend data - END OF DAY data only", "error")
            return

        self.current_symbol = symbol
        self.current_date_str = selected_date.strftime("%Y-%m-%d")
        self.update_status(f"Fetching END OF DAY options data for {symbol} on {selected_date} expiring {expiration_date}...", "loading")

        # Disable fetch button during operation
        self.fetch_button.setEnabled(False)

        try:
            # Get current price
            self.current_price = get_current_price(symbol)

            # Fetch options data
            calls_df, puts_df = fetch_options_for_date(symbol, expiration_date, self.current_price)

            # Combine calls and puts into a single DataFrame
            combined_data = []

            if calls_df is not None and not calls_df.empty:
                calls_copy = calls_df.copy()
                calls_copy['option_type'] = 'call'
                # Calculate dollar volume and dollar OI for calls
                calls_copy = self.calculate_dollar_values(calls_copy)
                combined_data.append(calls_copy)

            if puts_df is not None and not puts_df.empty:
                puts_copy = puts_df.copy()
                puts_copy['option_type'] = 'put'
                # Calculate dollar volume and dollar OI for puts
                puts_copy = self.calculate_dollar_values(puts_copy)
                combined_data.append(puts_copy)

            if combined_data:
                # Combine all data and sort by strike price
                self.options_data = pd.concat(combined_data, ignore_index=True)

                # Sort by option type (calls first), then by strike price
                self.options_data = self.options_data.sort_values(['option_type', 'strike'], ascending=[True, True])
                self.options_data = self.options_data.reset_index(drop=True)

                # Reorder columns to put option_type first and organize logically
                base_cols = ['option_type', 'strike', 'lastPrice', 'bid', 'ask']
                volume_cols = ['volume', 'dollar_volume', 'openInterest', 'dollar_oi']
                other_cols = [col for col in self.options_data.columns if col not in base_cols + volume_cols]
                cols = base_cols + volume_cols + other_cols

                # Only include columns that exist in the data
                cols = [col for col in cols if col in self.options_data.columns]
                self.options_data = self.options_data[cols]

                # Update summary display
                calls_count = len(calls_df) if calls_df is not None and not calls_df.empty else 0
                puts_count = len(puts_df) if puts_df is not None and not puts_df.empty else 0
                total_count = len(self.options_data)

                # Calculate some basic stats
                if not self.options_data.empty:
                    total_dollar_volume = self.options_data['dollar_volume'].sum()
                    total_dollar_oi = self.options_data['dollar_oi'].sum()

                    summary_text = f"""
<b>{symbol} Options Chain - {self.current_date_str}</b><br><br>
<b>Total Options:</b> {total_count} ({calls_count} calls, {puts_count} puts)<br>
<b>Underlying Price:</b> ${self.current_price:.2f}<br>
<b>Total Dollar Volume:</b> ${total_dollar_volume:,.0f}<br>
<b>Total Dollar OI:</b> ${total_dollar_oi:,.0f}
                    """
                else:
                    summary_text = f"""
<b>{symbol} Options Chain - {self.current_date_str}</b><br><br>
<b>Underlying Price:</b> ${self.current_price:.2f}<br>
<b>Status:</b> No options data available
                    """

                self.summary_info.setText(summary_text.strip())

                # Enable export button and update options table
                self.export_button.setEnabled(True)

                # Update the options table with the fetched data
                self.options_model.setData(self.options_data, "unified")

                self.update_status(f"Loaded {total_count} options ({calls_count} calls, {puts_count} puts) for {symbol} @ ${self.current_price:.2f}", "success")
            else:
                self.options_data = pd.DataFrame()
                self.summary_info.setText("No options data loaded")
                self.options_model.setData(pd.DataFrame(), "unified")
                self.export_button.setEnabled(False)
                self.update_status(f"No options data found for {symbol} expiring {expiration_date}", "warning")

        except Exception as e:
            logger.error(f"Error fetching options data: {e}")
            self.update_status(f"Error fetching options data: {str(e)}", "error")

        finally:
            # Re-enable fetch button
            self.fetch_button.setEnabled(True)

    def refresh_data(self):
        """Refresh the current options data."""
        if self.current_symbol:
            self.fetch_options_data()

    def toggle_auto_refresh(self, state):
        """Toggle auto-refresh functionality (disabled for EOD data)."""
        # Auto-refresh is disabled for END OF DAY data collection
        self.update_timer.stop()
        self.auto_refresh_check.setChecked(False)
        self.update_status("Auto-refresh not available for END OF DAY data collection", "warning")

    def update_status(self, message, status_type="info"):
        """Update the status label with appropriate styling."""
        colors = {
            "info": THEME_COLORS['text'],
            "success": THEME_COLORS['primary_accent'],
            "warning": THEME_COLORS['secondary_accent'],
            "error": THEME_COLORS['text'],
            "loading": THEME_COLORS['secondary_accent']
        }

        color = colors.get(status_type, THEME_COLORS['text'])
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"color: {color}; padding: 5px;")

    def apply_theme(self):
        """Apply the dark theme to the widget."""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
            }}
        """)

    def on_data_fetched_universal(self, symbol, data):
        """Handle data_fetched signal from universal_controls."""
        # Update symbol input if it's different
        if symbol != self.symbol_input.text().strip().upper():
            self.symbol_input.setText(symbol)

        # Optionally auto-fetch options data when new symbol is loaded
        # Uncomment the line below if you want automatic fetching
        # self.fetch_options_data()

    def export_data(self):
        """Export current options data to CSV."""
        if self.options_data.empty:
            self.update_status("No data to export", "warning")
            return

        # Get save location
        file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "Export Options Data",
            f"{self.current_symbol}_options_{datetime.now().strftime('%Y%m%d')}.csv",
            "CSV Files (*.csv)"
        )

        if file_path:
            try:
                # Export the unified options data
                self.options_data.to_csv(file_path, index=False)
                self.update_status(f"Data exported to {file_path}", "success")

            except Exception as e:
                logger.error(f"Error exporting data: {e}")
                self.update_status(f"Error exporting data: {str(e)}", "error")
